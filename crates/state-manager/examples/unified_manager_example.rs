//! 统一池管理器使用示例
//!
//! 演示如何使用新的统一架构管理多个 DEX 的池

use state_manager::core::types::*;
use state_manager::core::pool::*;
use state_manager::core::unified_manager::*;
use state_manager::utils::format::*;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use state_manager::core::GenericPoolManager;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 统一池管理器示例 ===\n");

    // 1. 创建统一管理器
    println!("1. 创建统一池管理器");
    let config = UnifiedManagerConfig {
        max_cache_size: 1000,
        cache_ttl_ms: 300_000, // 5分钟
        enable_auto_cleanup: true,
        cleanup_interval_ms: 60_000, // 1分钟
    };

    let mut unified_manager = UnifiedPoolManager::new(config);
    println!("   ✓ 统一管理器已创建");

    // 2. 创建示例池
    println!("\n2. 创建示例池");

    // 创建 Raydium CLMM 池
    let raydium_pool_id = PoolId::new(
        DexProtocol::Raydium,
        PoolType::Clmm,
        Pubkey::from_str("5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6")?,
    );

    let raydium_token_a = TokenReserve::new(
        Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?, // USDC
        1000_000_000, // 1000 USDC
        6,
    ).with_symbol("USDC".to_string());

    let raydium_token_b = TokenReserve::new(
        Pubkey::from_str("So11111111111111111111111111111111111111112")?, // SOL
        50_000_000_000, // 50 SOL
        9,
    ).with_symbol("SOL".to_string());

    let raydium_pool_state = GenericPoolState::new(
        DexProtocol::Raydium,
        PoolType::Clmm,
        raydium_pool_id.address,
        raydium_token_a,
        raydium_token_b,
        30, // 0.3% fee
    );

    let _raydium_manager = GenericPoolManager::new(raydium_pool_state);
    unified_manager.register_pool(raydium_pool_id.clone());

    println!("   ✓ Raydium CLMM 池已注册: {}", AddressFormatter::format_pool_id(&raydium_pool_id, true));

    // 创建 Meteora DLMM 池
    let meteora_pool_id = PoolId::new(
        DexProtocol::Meteora,
        PoolType::Dlmm,
        Pubkey::from_str("6rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS7")?,
    );

    let meteora_token_a = TokenReserve::new(
        Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?, // USDC
        2000_000_000, // 2000 USDC
        6,
    ).with_symbol("USDC".to_string());

    let meteora_token_b = TokenReserve::new(
        Pubkey::from_str("So11111111111111111111111111111111111111112")?, // SOL
        80_000_000_000, // 80 SOL
        9,
    ).with_symbol("SOL".to_string());

    let meteora_pool_state = GenericPoolState::new(
        DexProtocol::Meteora,
        PoolType::Dlmm,
        meteora_pool_id.address,
        meteora_token_a,
        meteora_token_b,
        25, // 0.25% fee
    );

    let _meteora_manager = GenericPoolManager::new(meteora_pool_state);
    unified_manager.register_pool(meteora_pool_id.clone());

    println!("   ✓ Meteora DLMM 池已注册: {}", AddressFormatter::format_pool_id(&meteora_pool_id, true));

    // 3. 查询统计信息
    println!("\n3. 全局统计信息");
    let stats = unified_manager.get_global_stats();
    println!("   总池数量: {}", stats.total_pools);
    println!("   活跃池数量: {}", stats.active_pools);
    println!("   按协议分布:");
    for (protocol, count) in &stats.pools_by_protocol {
        println!("     {}: {} 个池", protocol, count);
    }
    println!("   按类型分布:");
    for (pool_type, count) in &stats.pools_by_type {
        println!("     {}: {} 个池", pool_type, count);
    }

    // 4. 代币对查找
    println!("\n4. 代币对查找");
    let usdc_mint = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?;
    let sol_mint = Pubkey::from_str("So11111111111111111111111111111111111111112")?;

    let matching_pools = unified_manager.find_pools_for_tokens(&usdc_mint, &sol_mint);
    println!("   找到 {} 个支持 USDC/SOL 的池:", matching_pools.len());

    for pool_id in &matching_pools {
        println!("     {} - 价格: {} SOL/USDC",
                 AddressFormatter::format_pool_id(pool_id, true),
                 NumberFormatter::format_price(100.0, Some(6))); // 示例价格
    }

    // 5. 交换估算
    println!("\n5. 交换估算");
    let input_amount = 100_000_000; // 100 USDC
    println!("   输入: {} USDC", NumberFormatter::format_token_amount(input_amount, 6));

    match unified_manager.estimate_swap(&usdc_mint, &sol_mint, input_amount, Some(1.0)) {
        Ok(quotes) => {
            println!("   找到 {} 个报价:", quotes.len());
            for (i, quote) in quotes.iter().enumerate() {
                println!("     {}. 输出: {} SOL | 费用: {} USDC | 价格影响: {} | 路由: {}",
                         i + 1,
                         NumberFormatter::format_token_amount(quote.output_amount, 9),
                         NumberFormatter::format_token_amount(quote.fee_amount, 6),
                         NumberFormatter::format_percentage(quote.price_impact, Some(2)),
                         quote.route.iter().map(|id| AddressFormatter::format_pool_id(id, true)).collect::<Vec<_>>().join(" -> "));
            }
        }
        Err(e) => {
            println!("   交换估算失败: {}", e);
        }
    }

    // 6. 最佳报价
    println!("\n6. 最佳报价");
    match unified_manager.get_best_swap_quote(&usdc_mint, &sol_mint, input_amount, Some(1.0)) {
        Ok(best_quote) => {
            println!("   最佳报价:");
            println!("     输出: {} SOL", NumberFormatter::format_token_amount(best_quote.output_amount, 9));
            println!("     费用: {} USDC", NumberFormatter::format_token_amount(best_quote.fee_amount, 6));
            println!("     价格影响: {}", NumberFormatter::format_percentage(best_quote.price_impact, Some(2)));
            println!("     路由: {}", best_quote.route.iter().map(|id| AddressFormatter::format_pool_id(id, true)).collect::<Vec<_>>().join(" -> "));
            if let Some(min_output) = best_quote.minimum_output {
                println!("     最小输出: {} SOL", NumberFormatter::format_token_amount(min_output, 9));
            }
        }
        Err(e) => {
            println!("   获取最佳报价失败: {}", e);
        }
    }

    // 7. 按协议查询
    println!("\n7. 按协议查询");
    let raydium_pools = unified_manager.get_pools_by_protocol(DexProtocol::Raydium);
    println!("   Raydium 池数量: {}", raydium_pools.len());

    let meteora_pools = unified_manager.get_pools_by_protocol(DexProtocol::Meteora);
    println!("   Meteora 池数量: {}", meteora_pools.len());

    // 8. 按类型查询
    println!("\n8. 按类型查询");
    let clmm_pools = unified_manager.get_pools_by_type(PoolType::Clmm);
    println!("   CLMM 池数量: {}", clmm_pools.len());

    let dlmm_pools = unified_manager.get_pools_by_type(PoolType::Dlmm);
    println!("   DLMM 池数量: {}", dlmm_pools.len());

    // 9. 流动性排名
    println!("\n9. 流动性排名 (Top 5)");
    let top_pools = unified_manager.get_top_pools_by_liquidity(5);
    for (i, (pool_id, liquidity)) in top_pools.iter().enumerate() {
        println!("   {}. {} - {}",
                 i + 1,
                 AddressFormatter::format_pool_id(pool_id, true),
                 NumberFormatter::format_usd(*liquidity));
    }

    // 10. 池详细信息
    println!("\n10. 池详细信息");
    for pool_id in &matching_pools {
        println!("   池: {}", AddressFormatter::format_pool_id(pool_id, true));
        println!("     TVL: {}", NumberFormatter::format_usd(1000000.0));
        println!("     24h 交易量: {}", NumberFormatter::format_usd(50000.0));
        println!("     费率: {}", NumberFormatter::format_percentage(0.3, Some(2)));
        println!("     代币储备:");
        println!("       USDC: {}", NumberFormatter::format_token_amount(1000_000_000, 6));
        println!("       SOL: {}", NumberFormatter::format_token_amount(50_000_000_000, 9));
        println!("     当前价格: {} (A/B)", NumberFormatter::format_price(100.0, Some(6)));
        println!("     反向价格: {} (B/A)", NumberFormatter::format_price(0.01, Some(6)));
        println!();
    }

    println!("=== 示例完成 ===");
    Ok(())
}
