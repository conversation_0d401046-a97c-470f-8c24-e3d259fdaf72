//! Meteora DLMM 使用示例
//!
//! 演示如何使用 Meteora DLMM 状态管理和报价计算功能

use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use state_manager::{utils, MeteoraLbBin, MeteoraLbBinArray, MeteoraLbPairState, MeteoraLbPoolCache, MeteoraLbPoolManager};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Meteora DLMM 示例 ===\n");

    // 1. 创建示例池状态
    let pool_address = Pubkey::from_str("5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6")?;
    let token_x_mint = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?; // USDC
    let token_y_mint = Pubkey::from_str("So11111111111111111111111111111111111111112")?; // SOL

    println!("1. 创建 Meteora DLMM 池状态");
    let mut pool_state = MeteoraLbPairState::new(
        pool_address,
        token_x_mint,
        token_y_mint,
        6, // USDC decimals
        9, // SOL decimals
        8388608, // active_id (示例值)
        25, // bin_step (0.25%)
    );

    // 设置费率
    pool_state.base_fee_rate = 25; // 0.25%
    pool_state.max_fee_rate = 100; // 1%
    pool_state.protocol_fee_rate = 10; // 0.1%

    println!("   池地址: {}", pool_address);
    println!("   Token X (USDC): {}", token_x_mint);
    println!("   Token Y (SOL): {}", token_y_mint);
    println!("   活跃 Bin ID: {}", pool_state.active_id);
    println!("   Bin 步长: {}bp", pool_state.bin_step);
    println!("   基础费率: {}bp\n", pool_state.base_fee_rate);

    // 2. 创建示例 bin 数据
    println!("2. 添加流动性 Bin 数据");
    let mut bins = Vec::new();
    
    // 创建围绕活跃价格的多个bin
    for i in -5..=5 {
        let bin_id = pool_state.active_id + i;
        let price = pool_state.get_bin_price(bin_id)?;
        
        // 模拟流动性分布（中心附近流动性更多）
        let liquidity_multiplier = 6 - i.abs();
        let base_liquidity = 1_000_000_000u128;
        
        let bin = MeteoraLbBin {
            amount_x: if i <= 0 { 100_000 * liquidity_multiplier as u64 } else { 0 },
            amount_y: if i >= 0 { 50_000 * liquidity_multiplier as u64 } else { 0 },
            price,
            liquidity_supply: base_liquidity * liquidity_multiplier as u128,
            reward_per_token_stored: [0, 0],
            fee_amount_x_per_token_stored: 0,
            fee_amount_y_per_token_stored: 0,
            amount_x_in: 0,
            amount_y_in: 0,
        };
        
        bins.push(bin);
        
        let price_float = utils::q64::to_float(price);
        println!("   Bin {}: 价格 = {:.6}, X = {}, Y = {}, 流动性 = {}", 
                 bin_id, price_float, 
                 if i <= 0 { 100_000 * liquidity_multiplier as u64 } else { 0 },
                 if i >= 0 { 50_000 * liquidity_multiplier as u64 } else { 0 },
                 utils::NumberFormatter::format_liquidity(base_liquidity * liquidity_multiplier as u128));
    }

    // 创建 bin 数组
    let bin_array = MeteoraLbBinArray {
        index: 0,
        version: 1,
        padding: [0; 7],
        lb_pair: pool_address,
        bins,
    };

    pool_state.update_bin_array(0, bin_array);
    println!("   总流动性: {}\n", utils::NumberFormatter::format_liquidity(pool_state.get_total_liquidity()));

    // 3. 创建池管理器
    println!("3. 创建池管理器");
    let manager = MeteoraLbPoolManager::new(pool_state);
    
    let current_price = manager.get_current_price()?;
    println!("   当前价格: {:.6} SOL/USDC", current_price);
    
    let liquidity_info = manager.get_liquidity_info();
    println!("   活跃 Bin 数量: {}", liquidity_info.active_bin_count);
    println!("   价格范围: {:.6} - {:.6}", 
             utils::q64::to_float(liquidity_info.price_range.0),
             utils::q64::to_float(liquidity_info.price_range.1));

    // 4. 交换估算示例
    println!("\n4. 交换估算示例");
    
    // USDC -> SOL 交换
    let usdc_input = 1000_000_000; // 1000 USDC (6 decimals)
    println!("   输入: {} USDC", usdc_input as f64 / 1_000_000.0);
    
    match manager.estimate_swap_output(usdc_input, true, Some(5.0)) {
        Ok(estimation) => {
            println!("   预期输出: {} SOL", estimation.output_amount as f64 / 1_000_000_000.0);
            println!("   费用: {} USDC", estimation.fee_amount as f64 / 1_000_000.0);
            println!("   价格影响: {:.2}%", estimation.price_impact);
            println!("   使用的 Bin 数量: {}", estimation.bins_used.len());
            
            if estimation.remaining_input > 0 {
                println!("   未处理输入: {} USDC", estimation.remaining_input as f64 / 1_000_000.0);
            }
            
            // 显示详细的bin使用情况
            println!("   Bin 使用详情:");
            for bin_usage in &estimation.bins_used {
                println!("     Bin {}: 输入 {:.2} USDC -> 输出 {:.6} SOL (价格: {:.6})",
                         bin_usage.bin_id,
                         bin_usage.input_amount as f64 / 1_000_000.0,
                         bin_usage.output_amount as f64 / 1_000_000_000.0,
                         utils::q64::to_float(bin_usage.price));
            }
        }
        Err(e) => {
            println!("   交换估算失败: {}", e);
        }
    }

    // SOL -> USDC 交换
    println!("\n   反向交换示例:");
    let sol_input = 10_000_000_000; // 10 SOL (9 decimals)
    println!("   输入: {} SOL", sol_input as f64 / 1_000_000_000.0);
    
    match manager.estimate_swap_output(sol_input, false, Some(5.0)) {
        Ok(estimation) => {
            println!("   预期输出: {} USDC", estimation.output_amount as f64 / 1_000_000.0);
            println!("   费用: {} SOL", estimation.fee_amount as f64 / 1_000_000_000.0);
            println!("   价格影响: {:.2}%", estimation.price_impact);
            println!("   使用的 Bin 数量: {}", estimation.bins_used.len());
        }
        Err(e) => {
            println!("   交换估算失败: {}", e);
        }
    }

    // 5. 缓存管理示例
    println!("\n5. 缓存管理示例");
    let cache = MeteoraLbPoolCache::new();
    
    // 添加池到缓存
    cache.upsert_pool(pool_address, manager)?;
    println!("   已添加池到缓存");
    
    // 从缓存获取池
    if let Some(cached_manager) = cache.get_pool(&pool_address) {
        let cached_price = cached_manager.get_current_price()?;
        println!("   从缓存获取的价格: {:.6}", cached_price);
    }
    
    // 根据代币对查找池
    let pools = cache.find_pools_by_tokens(&token_x_mint, &token_y_mint);
    println!("   找到 {} 个匹配的池", pools.len());
    
    // 缓存统计
    let stats = cache.get_stats();
    println!("   缓存统计:");
    println!("     总池数: {}", stats.total_pools);
    println!("     活跃池数: {}", stats.active_pools);
    println!("     总流动性: {}", utils::NumberFormatter::format_liquidity(stats.total_liquidity));

    // 6. 流动性分布分析
    println!("\n6. 流动性分布分析");
    if let Some(manager) = cache.get_pool(&pool_address) {
        let distribution = manager.pool_state.get_liquidity_distribution();
        println!("   流动性分布 (前5个bin):");
        
        for (i, (bin_id, price, liquidity)) in distribution.iter().take(5).enumerate() {
            println!("     {}. Bin {}: 价格 {:.6}, 流动性 {}", 
                     i + 1, bin_id, utils::q64::to_float(*price), utils::NumberFormatter::format_liquidity(*liquidity));
        }
    }

    println!("\n=== 示例完成 ===");
    Ok(())
}

/// 辅助函数：格式化数字显示
fn format_number(num: f64) -> String {
    if num >= 1_000_000.0 {
        format!("{:.2}M", num / 1_000_000.0)
    } else if num >= 1_000.0 {
        format!("{:.2}K", num / 1_000.0)
    } else {
        format!("{:.2}", num)
    }
}
