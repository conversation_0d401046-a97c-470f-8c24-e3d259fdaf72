use std::fs;
use state_manager::{fee_math, fixed_point_math, CorePoolManager, RaydiumClmmPoolManager,
                    RaydiumClmmPoolState, SwapConfig, TickArrayBitmapExtension,
                    TickArrayState};

/// 根据价格计算 sqrt_price_x64 (Q64.64 格式)
fn calculate_sqrt_price_for_price(price: f64, decimals_0: u8, decimals_1: u8) -> u128 {
    // 调整小数位差异：SOL (9位) -> USDT (6位)
    // 实际价格需要考虑小数位差异：price * 10^(decimals_0 - decimals_1)
    let decimals_diff = decimals_0 as i32 - decimals_1 as i32;
    let adjusted_price = price * 10f64.powi(decimals_diff);

    // 计算 sqrt_price
    let sqrt_price = adjusted_price.sqrt();

    // 转换为 Q64.64 格式
    (sqrt_price * (1u128 << 64) as f64) as u128
}

/// 根据价格计算对应的 tick
fn calculate_tick_for_price(price: f64, decimals_0: u8, decimals_1: u8) -> i32 {
    // 调整小数位差异：SOL (9位) -> USDT (6位)
    let decimals_diff = decimals_0 as i32 - decimals_1 as i32;
    let adjusted_price = price * 10f64.powi(decimals_diff);

    // tick = log(price) / log(1.0001)
    let tick = (adjusted_price.ln() / 1.0001f64.ln()) as i32;

    // 确保 tick 在有效范围内
    tick.max(-443636).min(443636)
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Raydium CLMM 生产级交换计算示例");

    // 创建测试池状态
    let pool_state = RaydiumClmmPoolState {
        pool_id: Default::default(),
        amm_config: Default::default(),
        owner: Default::default(),
        token_mint_0: Default::default(),
        token_mint_1: Default::default(),
        token_vault_0: Default::default(),
        token_vault_1: Default::default(),
        observation_key: Default::default(),
        mint_decimals_0: 9,
        mint_decimals_1: 6,
        liquidity: 173467514525734, // 大量流动性
        fee_rate: 0,
        active: false,
        sqrt_price_x64: 7413295978905907703,
        tick_current: -18234,
        fee_growth_global_0_x64: 0,
        fee_growth_global_1_x64: 0,
        protocol_fees_token_0: 0,
        protocol_fees_token_1: 0,
        swap_in_amount_token_0: 0,
        swap_out_amount_token_1: 0,
        swap_in_amount_token_1: 0,
        status: 1,
        reward_infos: vec![],
        tick_array_bitmap: [
            13233894920445497344,
            18443928426025844659,
            18446744073709551615,
            18446744073709551615,
            13835053657235652607,
            292743606805363175,
            72070788177467936,
            1075839120,
            1073807377,
            72057594037927936,
            0,
            0,
            0,
            0,
            0,
            0
        ],
        total_fees_token_0: 0,
        total_fees_claimed_token_0: 0,
        total_fees_token_1: 0,
        total_fees_claimed_token_1: 0,
        fund_fees_token_0: 0,
        fund_fees_token_1: 0,
        open_time: 0,
        tick_spacing: 1,
        swap_out_amount_token_0: 0,
        recent_epoch: 0,
    };

    let mut manager = RaydiumClmmPoolManager::new(pool_state);

    let tick_state = fs::read_to_string("/Users/<USER>/code/rust-project/echoes/crates/state-manager/src/data/raydium/clmm/raydium-pool-tick-array-state.json")?;
    let tick_array_json: serde_json::Value = serde_json::from_str(&tick_state)?;
    let array_state = TickArrayState::from_json_value(&tick_array_json)?;
    manager.upsert_tick_array(array_state);

    // 读取 bitmap extension数据
    let bitmap_extension_str = fs::read_to_string("/Users/<USER>/code/rust-project/echoes/crates/state-manager/src/data/raydium/clmm/raydium-tick-array-bitmap-extension.json")?;
    let bitmap_extension_json: serde_json::Value = serde_json::from_str(&bitmap_extension_str)?;
    let bitmap_extension = TickArrayBitmapExtension::from_json_value(&bitmap_extension_json)?;
    manager.set_bitmap_extension(bitmap_extension);


    // 验证价格计算
    let current_price = manager.get_current_price();
    println!("计算得到的当前价格: {:?} SOL/USDT", current_price);
    println!("预期价格: 161.78 SOL/USDT");
    println!("价格差异: {:.2} ({:.2}%)",
             current_price - 161.78,
             ((current_price - 161.78) / 161.78 * 100.0).abs());

    // 调试信息
    let pool_sqrt_price = manager.get_state().sqrt_price_x64;
    let q64_64_one = 18446744073709551616u128; // 2^64
    println!("调试信息:");
    println!("  sqrt_price_x64: {}", pool_sqrt_price);
    println!("  Q64_64_ONE: {}", q64_64_one);
    println!("  sqrt_price_ratio: {:.10}", pool_sqrt_price as f64 / q64_64_one as f64);
    println!("  calculated_price: {:.10}", (pool_sqrt_price as f64 / q64_64_one as f64).powi(2));
    println!("  calculated_price target: {:.10}", (7412616392589177856.0f64 / q64_64_one as f64).powi(2) * 1000.0);
    println!("  calculated_price next tick price: {:.10}", (7412616392589177856.0f64 / q64_64_one as f64).powi(2) * 1000.0);
    println!("  calculated_price targe: {:.10}", (7396212433895373516.0f64 / q64_64_one as f64).powi(2) * 1000.0);
    println!("  calculated_price targe: {:.10}", (7413124752820184019.0f64 / q64_64_one as f64).powi(2) * 1000.0);

    // 示例 1: 基本交换估算
    println!("\n=== 基本交换估算 ===");
    let input_amount = 10000_000_000_000; // 100 SOL (9 decimals) - 使用更小金额避免滑点过大

    match manager.estimate_swap_output(input_amount, true, None) {
        Ok(result) => {
            println!("输入: {} lamports (0.1 SOL)", input_amount);
            println!("输出: {} micro USDT", result.amount_out);
            println!("预期输出: ~{:.0} micro USDT (0.1 * 167.17)", 0.1 * 161.78 * 1_000_000.0);
            println!("费用: {} lamports", result.fee_amount);
            println!("价格影响: {} bps", result.price_impact_bps);

            // 计算实际兑换率
            let sol_amount = input_amount as f64 / 1e9; // 转换为 SOL
            let usdt_amount = result.amount_out as f64 / 1e6; // 转换为 USDT
            let actual_rate = usdt_amount / sol_amount;
            println!("实际兑换率: {:.6} USDT/SOL", actual_rate);
            println!("SOL 数量: {:.4}, USDT 数量: {:.6}", sol_amount, usdt_amount);
            println!("价格差异: {:.4} ({:.4}%)",
                     actual_rate - current_price,
                     ((actual_rate - current_price) / current_price * 100.0).abs());
        }
        Err(e) => println!("交换估算失败: {}", e),
    }

    // 示例 2: 带配置的交换估算
    println!("\n=== 带配置的交换估算 ===");
    let config = SwapConfig {
        fee_rate: 500,  // 0.05%
        protocol_fee_rate: 0,
        max_slippage_bps: 500, // 5% - 更宽松的滑点设置
        exact_in: true,
    };

    match manager.estimate_swap_output_with_config(input_amount, true, None, &config) {
        Ok(result) => {
            println!("低费用配置:");
            println!("输入: {} lamports", input_amount);
            println!("输出: {} micro USDC", result.amount_out);
            println!("费用: {} lamports", result.fee_amount);
            println!("价格影响: {} bps", result.price_impact_bps);
        }
        Err(e) => println!("配置交换估算失败: {}", e),
    }

    // 示例 3: 参数验证
    println!("\n=== 参数验证 ===");
    match manager.validate_swap_params(0, true, None) {
        Ok(_) => println!("验证通过"),
        Err(e) => println!("验证失败: {}", e),
    }

    match manager.validate_swap_params(input_amount, true, None) {
        Ok(_) => println!("有效参数验证通过"),
        Err(e) => println!("验证失败: {}", e),
    }

    // 示例 4: 最大交换量估算
    println!("\n=== 最大交换量估算 ===");
    match manager.estimate_max_swap_amount(true, None) {
        Ok(max_amount) => {
            println!("最大可交换量: {} lamports", max_amount);
            println!("约 {} SOL", max_amount as f64 / 1e9);
        }
        Err(e) => println!("最大交换量估算失败: {}", e),
    }

    // 示例 5: 数学函数测试
    println!("\n=== 数学函数测试 ===");

    // 测试 tick 到 sqrt_price 转换
    let test_tick = 1000;
    match fixed_point_math::tick_to_sqrt_price(test_tick) {
        Ok(sqrt_price) => {
            println!("Tick {} -> sqrt_price: {}", test_tick, sqrt_price);

            // 反向转换
            match fixed_point_math::sqrt_price_to_tick(sqrt_price) {
                Ok(converted_tick) => {
                    println!("sqrt_price {} -> tick: {}", sqrt_price, converted_tick);
                    println!("转换精度: {} tick差异", (test_tick - converted_tick).abs());
                }
                Err(e) => println!("反向转换失败: {}", e),
            }
        }
        Err(e) => println!("Tick转换失败: {}", e),
    }

    // 示例 6: 费用计算
    println!("\n=== 费用计算 ===");
    let amount = 1_000_000;
    let fee_rate = 3000; // 0.3%
    let fee = fee_math::compute_fee_amount(amount, fee_rate);
    println!("金额: {}, 费用率: {} bps, 费用: {}", amount, fee_rate, fee);

    // 示例 7: 池统计信息
    println!("\n=== 池统计信息 ===");
    let stats = manager.get_pool_stats();
    println!("总流动性: {}", stats.total_liquidity);
    println!("当前tick: {}", stats.current_tick);
    println!("当前价格: {}", stats.current_price);
    println!("Tick数组数量: {}", stats.tick_arrays_count);

    println!("\n=== 示例完成 ===");
    Ok(())
}
