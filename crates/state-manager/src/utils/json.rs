//! JSON 处理工具
//!
//! 提供 JSON 数据解析和处理功能

use crate::core::types::*;
use serde_json::Value;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;

/// JSON 解析工具
pub struct JsonParser;

impl JsonParser {
    /// 安全地从 JSON 中提取字符串
    pub fn extract_string(value: &Value, key: &str) -> PoolResult<String> {
        value
            .get(key)
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid string field: {}", key)))
    }

    /// 安全地从 JSON 中提取可选字符串
    pub fn extract_optional_string(value: &Value, key: &str) -> Option<String> {
        value
            .get(key)
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
    }

    /// 安全地从 JSON 中提取 u64
    pub fn extract_u64(value: &Value, key: &str) -> PoolResult<u64> {
        value
            .get(key)
            .and_then(|v| {
                // 尝试直接解析数字
                if let Some(num) = v.as_u64() {
                    Some(num)
                } else if let Some(s) = v.as_str() {
                    // 尝试从字符串解析
                    s.parse().ok()
                } else {
                    None
                }
            })
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid u64 field: {}", key)))
    }

    /// 安全地从 JSON 中提取 u128
    pub fn extract_u128(value: &Value, key: &str) -> PoolResult<u128> {
        value
            .get(key)
            .and_then(|v| {
                if let Some(s) = v.as_str() {
                    s.parse().ok()
                } else if let Some(num) = v.as_u64() {
                    Some(num as u128)
                } else {
                    None
                }
            })
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid u128 field: {}", key)))
    }

    /// 安全地从 JSON 中提取 i32
    pub fn extract_i32(value: &Value, key: &str) -> PoolResult<i32> {
        value
            .get(key)
            .and_then(|v| {
                if let Some(num) = v.as_i64() {
                    Some(num as i32)
                } else if let Some(s) = v.as_str() {
                    s.parse().ok()
                } else {
                    None
                }
            })
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid i32 field: {}", key)))
    }

    /// 安全地从 JSON 中提取 u16
    pub fn extract_u16(value: &Value, key: &str) -> PoolResult<u16> {
        value
            .get(key)
            .and_then(|v| {
                if let Some(num) = v.as_u64() {
                    Some(num as u16)
                } else if let Some(s) = v.as_str() {
                    s.parse().ok()
                } else {
                    None
                }
            })
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid u16 field: {}", key)))
    }

    /// 安全地从 JSON 中提取 u8
    pub fn extract_u8(value: &Value, key: &str) -> PoolResult<u8> {
        value
            .get(key)
            .and_then(|v| {
                if let Some(num) = v.as_u64() {
                    Some(num as u8)
                } else if let Some(s) = v.as_str() {
                    s.parse().ok()
                } else {
                    None
                }
            })
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid u8 field: {}", key)))
    }

    /// 安全地从 JSON 中提取 f64
    pub fn extract_f64(value: &Value, key: &str) -> PoolResult<f64> {
        value
            .get(key)
            .and_then(|v| {
                if let Some(num) = v.as_f64() {
                    Some(num)
                } else if let Some(s) = v.as_str() {
                    s.parse().ok()
                } else {
                    None
                }
            })
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid f64 field: {}", key)))
    }

    /// 安全地从 JSON 中提取 Pubkey
    pub fn extract_pubkey(value: &Value, key: &str) -> PoolResult<Pubkey> {
        let pubkey_str = Self::extract_string(value, key)?;
        Pubkey::from_str(&pubkey_str)
            .map_err(|e| PoolError::ParseError(format!("Invalid pubkey {}: {}", pubkey_str, e)))
    }

    /// 安全地从 JSON 中提取可选 Pubkey
    pub fn extract_optional_pubkey(value: &Value, key: &str) -> PoolResult<Option<Pubkey>> {
        if let Some(pubkey_str) = Self::extract_optional_string(value, key) {
            if pubkey_str.is_empty() || pubkey_str == "11111111111111111111111111111111" {
                Ok(None)
            } else {
                Pubkey::from_str(&pubkey_str)
                    .map(Some)
                    .map_err(|e| PoolError::ParseError(format!("Invalid pubkey {}: {}", pubkey_str, e)))
            }
        } else {
            Ok(None)
        }
    }

    /// 安全地从 JSON 中提取数组
    pub fn extract_array<'a>(value: &'a Value, key: &str) -> PoolResult<&'a Vec<Value>> {
        value
            .get(key)
            .and_then(|v| v.as_array())
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid array field: {}", key)))
    }

    /// 安全地从 JSON 中提取对象
    pub fn extract_object<'a>(value: &'a Value, key: &str) -> PoolResult<&'a serde_json::Map<String, Value>> {
        value
            .get(key)
            .and_then(|v| v.as_object())
            .ok_or_else(|| PoolError::ParseError(format!("Missing or invalid object field: {}", key)))
    }

    /// 从嵌套路径提取值
    pub fn extract_nested<'a>(value: &'a Value, path: &[&str]) -> PoolResult<&'a Value> {
        let mut current = value;
        for &key in path {
            current = current
                .get(key)
                .ok_or_else(|| PoolError::ParseError(format!("Missing nested field: {}", key)))?;
        }
        Ok(current)
    }

    /// 解析 Solana 账户数据格式
    pub fn parse_account_data(json_str: &str) -> PoolResult<Value> {
        serde_json::from_str(json_str)
            .map_err(|e| PoolError::ParseError(format!("Failed to parse JSON: {}", e)))
    }

    /// 提取解析后的账户数据
    pub fn extract_parsed_data(value: &Value) -> PoolResult<&Value> {
        Self::extract_nested(value, &["parsed", "data"])
    }

    /// 提取账户信息
    pub fn extract_account_info(value: &Value) -> PoolResult<&Value> {
        Self::extract_nested(value, &["account"])
    }
}

/// JSON 构建工具
pub struct JsonBuilder;

impl JsonBuilder {
    /// 创建池状态的 JSON 表示
    pub fn build_pool_state_json(
        pool_id: &PoolId,
        token_a: &TokenReserve,
        token_b: &TokenReserve,
        additional_data: Option<&Value>,
    ) -> Value {
        let mut json = serde_json::json!({
            "poolId": {
                "protocol": pool_id.protocol.to_string(),
                "poolType": pool_id.pool_type.to_string(),
                "address": pool_id.address.to_string()
            },
            "tokenA": {
                "mint": token_a.mint.to_string(),
                "amount": token_a.amount.to_string(),
                "decimals": token_a.decimals,
                "symbol": token_a.symbol,
                "name": token_a.name
            },
            "tokenB": {
                "mint": token_b.mint.to_string(),
                "amount": token_b.amount.to_string(),
                "decimals": token_b.decimals,
                "symbol": token_b.symbol,
                "name": token_b.name
            }
        });

        if let Some(additional) = additional_data {
            if let Some(obj) = json.as_object_mut() {
                if let Some(additional_obj) = additional.as_object() {
                    for (key, value) in additional_obj {
                        obj.insert(key.clone(), value.clone());
                    }
                }
            }
        }

        json
    }

    /// 创建交换报价的 JSON 表示
    pub fn build_swap_quote_json(quote: &SwapQuote) -> Value {
        serde_json::json!({
            "inputToken": quote.input_token.to_string(),
            "outputToken": quote.output_token.to_string(),
            "inputAmount": quote.input_amount.to_string(),
            "outputAmount": quote.output_amount.to_string(),
            "feeAmount": quote.fee_amount.to_string(),
            "priceImpact": quote.price_impact,
            "direction": match quote.direction {
                SwapDirection::AToB => "AToB",
                SwapDirection::BToA => "BToA"
            },
            "minimumOutput": quote.minimum_output.map(|x| x.to_string()),
            "route": quote.route.iter().map(|id| id.to_string()).collect::<Vec<_>>()
        })
    }

    /// 创建错误响应的 JSON 表示
    pub fn build_error_json(error: &PoolError) -> Value {
        serde_json::json!({
            "error": {
                "type": match error {
                    PoolError::PoolNotFound(_) => "PoolNotFound",
                    PoolError::UnsupportedOperation(_) => "UnsupportedOperation",
                    PoolError::CalculationError(_) => "CalculationError",
                    PoolError::ParseError(_) => "ParseError",
                    PoolError::NetworkError(_) => "NetworkError",
                    PoolError::CacheError(_) => "CacheError",
                },
                "message": error.to_string()
            }
        })
    }
}

/// JSON 验证工具
pub struct JsonValidator;

impl JsonValidator {
    /// 验证池状态 JSON 的完整性
    pub fn validate_pool_state_json(value: &Value) -> PoolResult<()> {
        // 验证必需字段
        JsonParser::extract_nested(value, &["poolId", "protocol"])?;
        JsonParser::extract_nested(value, &["poolId", "poolType"])?;
        JsonParser::extract_nested(value, &["poolId", "address"])?;

        JsonParser::extract_nested(value, &["tokenA", "mint"])?;
        JsonParser::extract_nested(value, &["tokenA", "amount"])?;
        JsonParser::extract_nested(value, &["tokenA", "decimals"])?;

        JsonParser::extract_nested(value, &["tokenB", "mint"])?;
        JsonParser::extract_nested(value, &["tokenB", "amount"])?;
        JsonParser::extract_nested(value, &["tokenB", "decimals"])?;

        Ok(())
    }

    /// 验证交换参数 JSON
    pub fn validate_swap_params_json(value: &Value) -> PoolResult<()> {
        JsonParser::extract_string(value, "inputToken")?;
        JsonParser::extract_string(value, "outputToken")?;
        JsonParser::extract_u64(value, "inputAmount")?;

        // 验证方向
        let direction = JsonParser::extract_string(value, "direction")?;
        match direction.as_str() {
            "AToB" | "BToA" => Ok(()),
            _ => Err(PoolError::ParseError("Invalid swap direction".to_string())),
        }
    }

    /// 验证 Pubkey 格式
    pub fn validate_pubkey_format(pubkey_str: &str) -> PoolResult<()> {
        Pubkey::from_str(pubkey_str)
            .map(|_| ())
            .map_err(|e| PoolError::ParseError(format!("Invalid pubkey format: {}", e)))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_json_parser() {
        let json = json!({
            "stringField": "test",
            "numberField": 123,
            "pubkeyField": "11111111111111111111111111111111"
        });

        assert_eq!(JsonParser::extract_string(&json, "stringField").unwrap(), "test");
        assert_eq!(JsonParser::extract_u64(&json, "numberField").unwrap(), 123);
        assert!(JsonParser::extract_pubkey(&json, "pubkeyField").is_ok());
    }

    #[test]
    fn test_json_builder() {
        let pool_id = PoolId::new(
            DexProtocol::Raydium,
            PoolType::Clmm,
            Pubkey::from_str("11111111111111111111111111111111").unwrap(),
        );

        let token_a = TokenReserve::new(
            Pubkey::from_str("11111111111111111111111111111111").unwrap(),
            1000,
            6,
        );

        let token_b = TokenReserve::new(
            Pubkey::from_str("11111111111111111111111111111111").unwrap(),
            2000,
            9,
        );

        let json = JsonBuilder::build_pool_state_json(&pool_id, &token_a, &token_b, None);
        assert!(JsonValidator::validate_pool_state_json(&json).is_ok());
    }
}
