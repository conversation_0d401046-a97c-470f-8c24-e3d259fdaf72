# NumberFormatter 优化指南

## 文档概述

本文档分析了 `crates/state-manager/src/utils/format.rs` 中 `NumberFormatter` 的当前实现，识别了关键的架构和性能问题，并提供了详细的优化建议和重构方案。

## 目录

- [当前实现分析](#当前实现分析)
- [识别的问题](#识别的问题)
- [优化建议](#优化建议)
- [重构方案](#重构方案)
- [实施路线图](#实施路线图)
- [代码示例](#代码示例)
- [性能考量](#性能考量)
- [最佳实践](#最佳实践)

## 当前实现分析

### 现状概览

当前的 `NumberFormatter` 实现是一个单一的大型结构体，包含了多种格式化功能：

```rust
pub struct NumberFormatter;

impl NumberFormatter {
    // 处理大数字格式化
    pub fn format_large_number(num: u64) -> String { ... }
    
    // 处理价格格式化
    pub fn format_price(price: f64, precision: Option<usize>) -> String { ... }
    
    // 处理百分比格式化
    pub fn format_percentage(value: f64, precision: Option<usize>) -> String { ... }
    
    // 处理货币格式化
    pub fn format_currency(amount: f64, symbol: &str, precision: Option<usize>) -> String { ... }
    
    // 处理USD格式化
    pub fn format_usd(amount: f64) -> String { ... }
}
```

### 当前实现优势

1. **功能完整性**：覆盖了数字格式化的主要使用场景
2. **统一接口**：所有格式化功能集中在一个结构体中
3. **测试覆盖**：包含了较为完整的单元测试
4. **文档完善**：有中文注释和文档字符串

### 架构特点

- 使用静态方法模式
- 包含两套数字单位系统（`STANDARD_UNITS` 和 `EXTENDED_UNITS`）
- 通用格式化逻辑封装在 `format_with_units` 方法中
- 支持不同精度和阈值的数值处理

## 识别的问题

### 1. 架构设计问题

#### 单一职责原则违反
当前实现将多种不同类型的格式化逻辑混合在一个结构体中：
- 数字格式化（K/M/B/T转换）
- 货币格式化（美元、其他货币）
- 百分比格式化（正负值、精度处理）
- 代币数量格式化（小数位处理）

```rust
// 问题示例：单一结构体承担过多责任
impl NumberFormatter {
    pub fn format_large_number(num: u64) -> String { ... }    // 数字责任
    pub fn format_price(price: f64, precision: Option<usize>) -> String { ... }  // 价格责任
    pub fn format_percentage(value: f64, precision: Option<usize>) -> String { ... }  // 百分比责任
    pub fn format_usd(amount: f64) -> String { ... }          // 货币责任
}
```

#### 缺乏扩展性
硬编码的实现方式使得添加新的格式化类型或自定义规则变得困难。

### 2. 代码重复问题

#### K/M/B/T转换逻辑重复
多个方法中存在相似的数值缩放逻辑：

```rust
// format_price 方法中
if price >= 1_000_000.0 {
    format!("{:.2}M", price / 1_000_000.0)
} else if price >= 1_000.0 {
    format!("{:.2}K", price / 1_000.0)
}

// format_usd 方法中
if amount >= 1_000_000_000.0 {
    format!("{:.2}B", amount / 1_000_000_000.0)
} else if amount >= 1_000_000.0 {
    format!("{:.2}M", amount / 1_000_000.0)
}

// format_currency 方法中
if amount >= 1_000_000_000.0 {
    format!("{:.2}B", amount / 1_000_000_000.0)
} else if amount >= 1_000_000.0 {
    format!("{:.2}M", amount / 1_000_000.0)
}
```

#### 精度处理重复
不同方法中包含类似的精度和阈值判断逻辑。

### 3. 硬编码问题

#### 魔法数字
代码中包含大量硬编码的阈值和精度值：

```rust
// 硬编码阈值
if price >= 1_000_000.0 { ... }
if price >= 1_000.0 { ... }
if price >= 1.0 { ... }
if price >= 0.001 { ... }

// 硬编码精度
format!("{:.2}M", ...)
format!("{:.6}", ...)
format!("{:.8}", ...)
```

#### 单位系统固定
单位后缀（K, M, B, T）硬编码为英文，无法支持其他语言或地区。

### 4. 国际化支持缺失

#### 单语言支持
当前只支持英文单位标识（K、M、B、T），无法适应不同地区的格式化需求。

#### 货币符号限制
货币格式化功能虽然接受自定义符号，但缺乏完整的区域设置支持。

### 5. 错误处理不足

#### 边界值处理
```rust
// 当前实现缺乏对以下情况的处理：
- NaN 值
- Infinity 值
- 负数的特殊处理
- 超大数值的溢出处理
```

#### 类型转换风险
```rust
// 存在精度丢失风险的转换
let formatted_value = value as f64 / unit.divisor;  // u128 -> f64 可能丢失精度
```

### 6. 性能问题

#### 线性搜索
```rust
// format_with_units 中的线性搜索
for unit in units {
    if value >= unit.threshold {
        // 找到第一个匹配的单位
    }
}
```

对于频繁调用的场景，线性搜索可能成为性能瓶颈。

#### 字符串分配开销
每次格式化都会创建新的字符串，缺乏缓存机制。

### 7. 类型安全问题

#### 混合数值类型
代码中混合使用了 `u64`、`u128`、`f64` 等不同类型：

```rust
pub fn format_large_number(num: u64) -> String {
    Self::format_with_units(num as u128, ...)  // u64 -> u128
}

fn format_with_units(value: u128, units: &[NumberUnit], precision: usize) -> String {
    let formatted_value = value as f64 / unit.divisor;  // u128 -> f64
}
```

这种转换可能导致精度丢失或数值范围问题。

## 优化建议

### 1. 架构重构建议

#### 分离关注点
将单一的 `NumberFormatter` 拆分为专门的格式化器：

```rust
pub struct NumberFormatter {
    config: NumberFormatConfig,
}

pub struct CurrencyFormatter {
    config: CurrencyFormatConfig,
    locale: Locale,
}

pub struct PercentageFormatter {
    config: PercentageFormatConfig,
}

pub struct TokenAmountFormatter {
    config: TokenFormatConfig,
}
```

#### 配置驱动设计
引入配置结构体来替代硬编码值：

```rust
#[derive(Debug, Clone)]
pub struct NumberFormatConfig {
    pub units: Vec<NumberUnit>,
    pub default_precision: usize,
    pub small_number_threshold: f64,
    pub large_number_threshold: f64,
    pub scientific_notation_threshold: f64,
}

impl Default for NumberFormatConfig {
    fn default() -> Self {
        Self {
            units: vec![
                NumberUnit { threshold: 1_000_000_000_000, divisor: 1_000_000_000_000.0, suffix: "T" },
                NumberUnit { threshold: 1_000_000_000, divisor: 1_000_000_000.0, suffix: "B" },
                NumberUnit { threshold: 1_000_000, divisor: 1_000_000.0, suffix: "M" },
                NumberUnit { threshold: 1_000, divisor: 1_000.0, suffix: "K" },
            ],
            default_precision: 2,
            small_number_threshold: 0.001,
            large_number_threshold: 1_000.0,
            scientific_notation_threshold: 1e-6,
        }
    }
}
```

### 2. 消除代码重复

#### 提取通用格式化方法
创建统一的数值缩放逻辑：

```rust
impl NumberFormatter {
    fn format_with_scale(&self, value: f64, precision: Option<usize>) -> String {
        let precision = precision.unwrap_or(self.config.default_precision);
        
        // 处理特殊值
        if value.is_nan() {
            return "NaN".to_string();
        }
        if value.is_infinite() {
            return if value.is_sign_positive() { "∞" } else { "-∞" }.to_string();
        }
        
        let abs_value = value.abs();
        
        // 使用配置的单位进行格式化
        for unit in &self.config.units {
            if abs_value >= unit.threshold as f64 {
                let scaled_value = value / unit.divisor;
                return format!("{:.prec$}{}", scaled_value, unit.suffix, prec = precision);
            }
        }
        
        // 处理小数值
        if abs_value < self.config.small_number_threshold && abs_value > 0.0 {
            format!("{:.prec$e}", value, prec = precision)
        } else {
            format!("{:.prec$}", value, prec = precision)
        }
    }
}
```

### 3. 增强类型安全

#### 使用精确数值库
引入 `rust_decimal` 库来处理高精度计算：

```rust
use rust_decimal::Decimal;

pub struct PreciseNumberFormatter {
    config: NumberFormatConfig,
}

impl PreciseNumberFormatter {
    pub fn format_precise(&self, value: Decimal) -> String {
        // 使用 Decimal 进行精确计算，避免浮点数精度问题
    }
}
```

#### 泛型设计
使用泛型和 trait 来提高类型安全性：

```rust
pub trait Formattable {
    fn to_decimal(self) -> Decimal;
}

impl Formattable for u64 {
    fn to_decimal(self) -> Decimal {
        Decimal::from(self)
    }
}

impl Formattable for f64 {
    fn to_decimal(self) -> Decimal {
        Decimal::from_f64(self).unwrap_or_default()
    }
}

impl NumberFormatter {
    pub fn format<T: Formattable>(&self, value: T) -> String {
        let decimal_value = value.to_decimal();
        self.format_decimal(decimal_value)
    }
}
```

### 4. 添加国际化支持

#### 本地化配置
```rust
#[derive(Debug, Clone)]
pub struct LocaleConfig {
    pub decimal_separator: char,
    pub thousands_separator: Option<char>,
    pub currency_symbol: String,
    pub currency_position: CurrencyPosition,
    pub units: HashMap<String, String>,  // "K" -> "千", "M" -> "万"
}

#[derive(Debug, Clone)]
pub enum CurrencyPosition {
    Before,
    After,
}

impl LocaleConfig {
    pub fn us_english() -> Self {
        Self {
            decimal_separator: '.',
            thousands_separator: Some(','),
            currency_symbol: "$".to_string(),
            currency_position: CurrencyPosition::Before,
            units: [
                ("K".to_string(), "K".to_string()),
                ("M".to_string(), "M".to_string()),
                ("B".to_string(), "B".to_string()),
                ("T".to_string(), "T".to_string()),
            ].into(),
        }
    }
    
    pub fn simplified_chinese() -> Self {
        Self {
            decimal_separator: '.',
            thousands_separator: Some(','),
            currency_symbol: "¥".to_string(),
            currency_position: CurrencyPosition::Before,
            units: [
                ("K".to_string(), "千".to_string()),
                ("M".to_string(), "万".to_string()),
                ("B".to_string(), "十亿".to_string()),
                ("T".to_string(), "万亿".to_string()),
            ].into(),
        }
    }
}
```

### 5. 性能优化

#### 二分搜索单位查找
```rust
impl NumberFormatter {
    fn find_appropriate_unit(&self, value: f64) -> Option<&NumberUnit> {
        let abs_value = value.abs() as u128;
        
        // 使用二分搜索查找合适的单位
        self.config.units.binary_search_by(|unit| {
            unit.threshold.cmp(&abs_value).reverse()
        }).map(|index| &self.config.units[index])
        .or_else(|index| {
            if index < self.config.units.len() {
                Some(&self.config.units[index])
            } else {
                None
            }
        })
    }
}
```

#### 格式化结果缓存
```rust
use std::collections::HashMap;

pub struct CachedNumberFormatter {
    formatter: NumberFormatter,
    cache: HashMap<(u64, usize), String>,  // (value, precision) -> formatted string
    cache_size_limit: usize,
}

impl CachedNumberFormatter {
    pub fn format_cached(&mut self, value: u64, precision: Option<usize>) -> String {
        let precision = precision.unwrap_or(2);
        let key = (value, precision);
        
        if let Some(cached) = self.cache.get(&key) {
            return cached.clone();
        }
        
        let formatted = self.formatter.format_with_scale(value as f64, Some(precision));
        
        if self.cache.len() < self.cache_size_limit {
            self.cache.insert(key, formatted.clone());
        }
        
        formatted
    }
}
```

### 6. 错误处理改进

#### 全面的边界值处理
```rust
#[derive(Debug, thiserror::Error)]
pub enum FormatError {
    #[error("Invalid value: {0}")]
    InvalidValue(String),
    
    #[error("Precision out of range: {0}")]
    InvalidPrecision(usize),
    
    #[error("Overflow in calculation")]
    Overflow,
}

pub type FormatResult<T> = Result<T, FormatError>;

impl NumberFormatter {
    pub fn format_safe(&self, value: f64, precision: Option<usize>) -> FormatResult<String> {
        // 检查输入值
        if value.is_nan() {
            return Err(FormatError::InvalidValue("NaN".to_string()));
        }
        
        // 检查精度范围
        if let Some(p) = precision {
            if p > 20 {
                return Err(FormatError::InvalidPrecision(p));
            }
        }
        
        // 检查数值范围
        if value.abs() > 1e308 {
            return Err(FormatError::Overflow);
        }
        
        Ok(self.format_internal(value, precision))
    }
}
```

## 重构方案

### 阶段一：模块化重构

#### 目标
将单一的 `NumberFormatter` 拆分为专门的格式化器模块。

#### 实现步骤

1. **创建基础 trait**
```rust
pub trait Formatter<T> {
    type Output;
    type Error;
    
    fn format(&self, value: T) -> Result<Self::Output, Self::Error>;
}
```

2. **实现专门的格式化器**
```rust
// 数字格式化器
pub struct NumberFormatter {
    config: NumberFormatConfig,
}

impl Formatter<f64> for NumberFormatter {
    type Output = String;
    type Error = FormatError;
    
    fn format(&self, value: f64) -> FormatResult<String> {
        self.format_safe(value, None)
    }
}

// 货币格式化器
pub struct CurrencyFormatter {
    config: CurrencyFormatConfig,
    locale: LocaleConfig,
}

impl Formatter<f64> for CurrencyFormatter {
    type Output = String;
    type Error = FormatError;
    
    fn format(&self, value: f64) -> FormatResult<String> {
        let number_part = self.number_formatter.format(value)?;
        Ok(self.apply_currency_format(number_part))
    }
}
```

### 阶段二：配置系统实现

#### 目标
实现灵活的配置系统，支持运行时自定义格式化规则。

#### 配置结构设计
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatConfig {
    pub number: NumberFormatConfig,
    pub currency: CurrencyFormatConfig,
    pub percentage: PercentageFormatConfig,
    pub locale: LocaleConfig,
}

impl FormatConfig {
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self, ConfigError> {
        let content = fs::read_to_string(path)?;
        let config: FormatConfig = serde_json::from_str(&content)?;
        Ok(config)
    }
    
    pub fn default_for_locale(locale: &str) -> Self {
        match locale {
            "en_US" => Self::us_english(),
            "zh_CN" => Self::simplified_chinese(),
            _ => Self::us_english(),
        }
    }
}
```

### 阶段三：性能优化实现

#### 目标
实现高性能的格式化逻辑，包括缓存和优化的查找算法。

#### 实现策略
```rust
pub struct OptimizedFormatSystem {
    formatters: HashMap<FormatType, Box<dyn FormatterTrait>>,
    cache: LruCache<FormatKey, String>,
    lookup_table: BTreeMap<u128, &'static str>,  // 预计算的单位查找表
}

impl OptimizedFormatSystem {
    pub fn new(config: FormatConfig) -> Self {
        let mut system = Self {
            formatters: HashMap::new(),
            cache: LruCache::new(1000),
            lookup_table: BTreeMap::new(),
        };
        
        // 预计算单位查找表
        system.build_lookup_table(&config);
        system.register_formatters(config);
        
        system
    }
    
    fn build_lookup_table(&mut self, config: &FormatConfig) {
        // 构建高效的单位查找表
        for unit in &config.number.units {
            self.lookup_table.insert(unit.threshold, unit.suffix);
        }
    }
}
```

### 阶段四：国际化和本地化

#### 目标
实现完整的多语言和多地区支持。

#### 实现方案
```rust
pub struct I18nFormatSystem {
    locales: HashMap<String, LocaleConfig>,
    current_locale: String,
    fallback_locale: String,
}

impl I18nFormatSystem {
    pub fn format_localized<T>(&self, value: T, format_type: FormatType) -> String 
    where
        T: Formattable,
    {
        let locale = self.get_current_locale();
        let formatter = self.get_formatter(format_type, locale);
        formatter.format(value)
    }
    
    pub fn set_locale(&mut self, locale: &str) -> Result<(), LocaleError> {
        if self.locales.contains_key(locale) {
            self.current_locale = locale.to_string();
            Ok(())
        } else {
            Err(LocaleError::UnsupportedLocale(locale.to_string()))
        }
    }
}
```

## 实施路线图

### 第一优先级（核心架构）
1. **模块拆分**（预计 1-2 周）
   - 拆分 NumberFormatter 为专门的格式化器
   - 实现基础的 trait 系统
   - 迁移现有功能并确保向后兼容

2. **配置系统**（预计 1 周）
   - 实现配置结构体
   - 支持运行时配置加载
   - 替换硬编码值

### 第二优先级（功能增强）
1. **错误处理改进**（预计 1 周）
   - 实现全面的错误类型
   - 添加边界值检查
   - 提供安全的格式化接口

2. **类型安全增强**（预计 1-2 周）
   - 引入精确数值计算
   - 实现泛型格式化 trait
   - 消除类型转换风险

### 第三优先级（性能和国际化）
1. **性能优化**（预计 2 周）
   - 实现缓存系统
   - 优化查找算法
   - 性能测试和调优

2. **国际化支持**（预计 2-3 周）
   - 设计本地化架构
   - 实现多语言单位支持
   - 添加常用地区配置

### 第四优先级（高级功能）
1. **插件系统**（预计 2 周）
   - 支持自定义格式化策略
   - 实现格式化器插件接口
   - 提供扩展示例

2. **高级缓存**（预计 1 周）
   - 实现智能缓存策略
   - 支持缓存统计和调优
   - 内存使用优化

## 代码示例

### 重构后的使用示例

```rust
use echoes_format::{FormatSystem, FormatConfig, FormatType};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化格式化系统
    let config = FormatConfig::default_for_locale("zh_CN");
    let mut format_system = FormatSystem::new(config);
    
    // 数字格式化
    let large_number = format_system.format(1_500_000_u64, FormatType::Number)?;
    println!("大数字: {}", large_number); // "1.50万"
    
    // 货币格式化
    let currency = format_system.format_currency(1234.56, "USD")?;
    println!("货币: {}", currency); // "¥1,234.56"
    
    // 百分比格式化
    let percentage = format_system.format_percentage(12.345, Some(2))?;
    println!("百分比: {}", percentage); // "12.35%"
    
    // 切换语言环境
    format_system.set_locale("en_US")?;
    let english_number = format_system.format(1_500_000_u64, FormatType::Number)?;
    println!("English number: {}", english_number); // "1.50M"
    
    Ok(())
}
```

### 配置文件示例

```json
{
    "number": {
        "units": [
            {"threshold": 1000000000000, "divisor": 1000000000000.0, "suffix": "万亿"},
            {"threshold": 1000000000, "divisor": 1000000000.0, "suffix": "十亿"},
            {"threshold": 10000, "divisor": 10000.0, "suffix": "万"},
            {"threshold": 1000, "divisor": 1000.0, "suffix": "千"}
        ],
        "default_precision": 2,
        "small_number_threshold": 0.001,
        "scientific_notation_threshold": 1e-6
    },
    "currency": {
        "default_symbol": "¥",
        "position": "before",
        "decimal_places": 2
    },
    "locale": {
        "decimal_separator": ".",
        "thousands_separator": ",",
        "currency_symbol": "¥",
        "currency_position": "before"
    }
}
```

### 自定义格式化器示例

```rust
use echoes_format::{Formatter, FormatResult};

pub struct ScientificNotationFormatter {
    precision: usize,
}

impl Formatter<f64> for ScientificNotationFormatter {
    type Output = String;
    type Error = FormatError;
    
    fn format(&self, value: f64) -> FormatResult<String> {
        if value.is_nan() {
            return Err(FormatError::InvalidValue("NaN".to_string()));
        }
        
        Ok(format!("{:.prec$e}", value, prec = self.precision))
    }
}

// 注册自定义格式化器
fn register_custom_formatter(system: &mut FormatSystem) {
    let scientific_formatter = ScientificNotationFormatter { precision: 3 };
    system.register_formatter("scientific", Box::new(scientific_formatter));
}
```

## 性能考量

### 基准测试建议

创建性能测试来验证优化效果：

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_current_formatter(c: &mut Criterion) {
    c.bench_function("current format_large_number", |b| {
        b.iter(|| {
            for i in 0..1000 {
                black_box(NumberFormatter::format_large_number(i * 1000));
            }
        })
    });
}

fn benchmark_optimized_formatter(c: &mut Criterion) {
    let formatter = OptimizedNumberFormatter::new(NumberFormatConfig::default());
    
    c.bench_function("optimized format_large_number", |b| {
        b.iter(|| {
            for i in 0..1000 {
                black_box(formatter.format(i * 1000));
            }
        })
    });
}

criterion_group!(benches, benchmark_current_formatter, benchmark_optimized_formatter);
criterion_main!(benches);
```

### 内存使用优化

1. **字符串池**：对于常用的格式化结果，使用字符串池来减少内存分配
2. **延迟计算**：只在需要时才进行复杂的格式化计算
3. **缓存策略**：实现 LRU 缓存来平衡内存使用和性能

## 最佳实践

### 1. 向后兼容性

在重构过程中确保现有 API 的向后兼容：

```rust
// 保留原有的静态方法接口
impl NumberFormatter {
    #[deprecated(note = "Use FormatSystem::format instead")]
    pub fn format_large_number(num: u64) -> String {
        static DEFAULT_SYSTEM: OnceLock<FormatSystem> = OnceLock::new();
        let system = DEFAULT_SYSTEM.get_or_init(|| FormatSystem::default());
        system.format(num, FormatType::Number).unwrap_or_else(|_| num.to_string())
    }
}
```

### 2. 测试策略

实现全面的测试覆盖：

```rust
#[cfg(test)]
mod integration_tests {
    use super::*;
    
    #[test]
    fn test_format_system_consistency() {
        let system = FormatSystem::default();
        
        // 测试各种边界值
        let test_cases = vec![
            (0_u64, "0"),
            (999_u64, "999"),
            (1_000_u64, "1.00K"),
            (1_000_000_u64, "1.00M"),
            (u64::MAX, "18.45E"), // 处理极大值
        ];
        
        for (input, expected) in test_cases {
            let result = system.format(input, FormatType::Number).unwrap();
            assert_eq!(result, expected, "Failed for input: {}", input);
        }
    }
    
    #[test]
    fn test_locale_switching() {
        let mut system = FormatSystem::new(FormatConfig::default());
        
        // 测试英文环境
        system.set_locale("en_US").unwrap();
        assert_eq!(system.format(1_000_000_u64, FormatType::Number).unwrap(), "1.00M");
        
        // 测试中文环境
        system.set_locale("zh_CN").unwrap();
        assert_eq!(system.format(1_000_000_u64, FormatType::Number).unwrap(), "100.00万");
    }
}
```

### 3. 文档和示例

提供清晰的文档和使用示例：

```rust
/// 高性能的数字格式化系统
/// 
/// # 示例
/// 
/// ```rust
/// use echoes_format::{FormatSystem, FormatType};
/// 
/// let system = FormatSystem::default();
/// let formatted = system.format(1_500_000_u64, FormatType::Number)?;
/// assert_eq!(formatted, "1.50M");
/// ```
/// 
/// # 性能特性
/// 
/// - 支持缓存以提高重复格式化的性能
/// - 使用二分搜索进行单位查找
/// - 支持批量格式化操作
pub struct FormatSystem {
    // ...
}
```

### 4. 错误处理最佳实践

```rust
// 提供多种错误处理选项
impl FormatSystem {
    /// 安全的格式化方法，返回 Result
    pub fn format_safe<T>(&self, value: T, format_type: FormatType) -> FormatResult<String>
    where T: Formattable;
    
    /// 便捷的格式化方法，失败时返回默认值
    pub fn format_or_default<T>(&self, value: T, format_type: FormatType) -> String
    where T: Formattable;
    
    /// 批量格式化，返回成功的结果和错误列表
    pub fn format_batch<T>(&self, values: &[T], format_type: FormatType) -> (Vec<String>, Vec<FormatError>)
    where T: Formattable + Clone;
}
```

## 总结

当前的 `NumberFormatter` 实现虽然功能完整，但存在明显的架构和性能问题。通过系统性的重构，可以实现：

1. **更好的代码组织**：通过模块化设计提高可维护性
2. **增强的灵活性**：通过配置驱动的设计支持运行时定制
3. **改进的性能**：通过优化算法和缓存机制提高执行效率
4. **更强的类型安全**：通过泛型和 trait 设计减少运行时错误
5. **国际化支持**：通过本地化配置支持多语言环境
6. **更好的错误处理**：通过全面的错误类型提供更好的用户体验

建议按照提供的路线图分阶段实施这些改进，优先处理核心架构问题，然后逐步添加高级功能。每个阶段都应该包含充分的测试和性能验证，确保改进的有效性。

通过这些优化，`NumberFormatter` 将成为一个高性能、灵活且易于维护的格式化系统，能够满足复杂应用场景的需求。