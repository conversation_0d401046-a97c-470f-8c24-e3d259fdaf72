//! 数学计算工具
//!
//! 提供各种数学计算函数，包括价格计算、流动性计算等

use crate::core::types::*;

/// 数学常量
pub mod constants {
    /// 基础点最大值 (100%)
    pub const BASIS_POINT_MAX: u64 = 10000;
    /// 价格精度位数
    pub const PRICE_PRECISION_BITS: u32 = 64;
    /// 最小价格变化
    pub const MIN_PRICE_CHANGE: f64 = 1e-18;
    /// 最大价格影响（百分比）
    pub const MAX_PRICE_IMPACT: f64 = 50.0;
}

/// Q64.64 格式价格工具
pub mod q64 {
    /// 将 Q64.64 格式转换为浮点数
    pub fn to_float(q64_price: u128) -> f64 {
        q64_price as f64 / (1u128 << 64) as f64
    }

    /// 将浮点数转换为 Q64.64 格式
    pub fn from_float(price: f64) -> u128 {
        (price * (1u128 << 64) as f64) as u128
    }

    /// 检查 Q64.64 价格是否有效
    pub fn is_valid(q64_price: u128) -> bool {
        q64_price > 0 && q64_price < u128::MAX / 2
    }
}

/// 价格计算工具
pub mod price {
    use super::*;

    /// 计算两个价格之间的百分比差异
    pub fn calculate_percentage_difference(price1: f64, price2: f64) -> f64 {
        if price1 == 0.0 || price2 == 0.0 {
            return 0.0;
        }

        let diff = (price1 - price2).abs();
        (diff / price1.min(price2)) * 100.0
    }

    /// 计算价格影响
    pub fn calculate_price_impact(
        price_before: f64,
        price_after: f64,
    ) -> f64 {
        if price_before == 0.0 {
            return 0.0;
        }

        let impact = ((price_before - price_after) / price_before * 100.0).abs();
        impact.min(constants::MAX_PRICE_IMPACT)
    }

    /// 应用小数位调整
    pub fn adjust_for_decimals(
        amount: u64,
        from_decimals: u8,
        to_decimals: u8,
    ) -> f64 {
        let from_factor = 10_f64.powi(from_decimals as i32);
        let to_factor = 10_f64.powi(to_decimals as i32);

        (amount as f64 / from_factor) * to_factor
    }

    /// 计算加权平均价格
    pub fn calculate_weighted_average_price(
        prices: &[(f64, f64)], // (price, weight)
    ) -> f64 {
        if prices.is_empty() {
            return 0.0;
        }

        let total_weighted_price: f64 = prices.iter()
            .map(|(price, weight)| price * weight)
            .sum();

        let total_weight: f64 = prices.iter()
            .map(|(_, weight)| weight)
            .sum();

        if total_weight == 0.0 {
            0.0
        } else {
            total_weighted_price / total_weight
        }
    }
}

/// 流动性计算工具
pub mod liquidity {
    use super::*;

    /// 计算恒定乘积池的流动性
    pub fn calculate_cpmm_liquidity(
        amount_a: u64,
        amount_b: u64,
        decimals_a: u8,
        decimals_b: u8,
    ) -> f64 {
        let adjusted_a = amount_a as f64 / 10_f64.powi(decimals_a as i32);
        let adjusted_b = amount_b as f64 / 10_f64.powi(decimals_b as i32);

        (adjusted_a * adjusted_b).sqrt()
    }

    /// 计算流动性价值（USD）
    pub fn calculate_liquidity_value_usd(
        amount_a: u64,
        amount_b: u64,
        decimals_a: u8,
        decimals_b: u8,
        price_a_usd: f64,
        price_b_usd: f64,
    ) -> f64 {
        let adjusted_a = amount_a as f64 / 10_f64.powi(decimals_a as i32);
        let adjusted_b = amount_b as f64 / 10_f64.powi(decimals_b as i32);

        adjusted_a * price_a_usd + adjusted_b * price_b_usd
    }

    /// 计算无常损失
    pub fn calculate_impermanent_loss(
        initial_price_ratio: f64,
        current_price_ratio: f64,
    ) -> f64 {
        if initial_price_ratio <= 0.0 || current_price_ratio <= 0.0 {
            return 0.0;
        }

        let ratio = current_price_ratio / initial_price_ratio;
        let sqrt_ratio = ratio.sqrt();

        let hodl_value = 0.5 * (1.0 + ratio);
        let lp_value = sqrt_ratio;

        if hodl_value == 0.0 {
            0.0
        } else {
            ((lp_value / hodl_value) - 1.0) * 100.0
        }
    }
}

/// 交换计算工具
pub mod swap {
    use super::*;

    /// 恒定乘积交换计算
    pub fn calculate_cpmm_output(
        input_amount: u64,
        input_reserve: u64,
        output_reserve: u64,
        fee_rate_bp: u16,
    ) -> PoolResult<u64> {
        if input_amount == 0 {
            return Err(PoolError::CalculationError("Input amount cannot be zero".to_string()));
        }

        if input_reserve == 0 || output_reserve == 0 {
            return Err(PoolError::CalculationError("Reserves cannot be zero".to_string()));
        }

        // 应用费用
        let fee_multiplier = (constants::BASIS_POINT_MAX - fee_rate_bp as u64) as f64 / constants::BASIS_POINT_MAX as f64;
        let input_after_fee = (input_amount as f64 * fee_multiplier) as u64;

        // 恒定乘积公式: (x + Δx) * (y - Δy) = x * y
        let k = input_reserve as u128 * output_reserve as u128;
        let new_input_reserve = input_reserve as u128 + input_after_fee as u128;

        if new_input_reserve == 0 {
            return Err(PoolError::CalculationError("New input reserve is zero".to_string()));
        }

        let new_output_reserve = k / new_input_reserve;
        let output_amount = output_reserve as u128 - new_output_reserve;

        Ok(output_amount.min(output_reserve as u128) as u64)
    }

    /// 计算最小输出（考虑滑点）
    pub fn calculate_minimum_output(
        expected_output: u64,
        slippage_tolerance_bp: u16,
    ) -> u64 {
        let slippage_multiplier = (constants::BASIS_POINT_MAX - slippage_tolerance_bp as u64) as f64 / constants::BASIS_POINT_MAX as f64;
        (expected_output as f64 * slippage_multiplier) as u64
    }

    /// 验证交换参数
    pub fn validate_swap_params(
        input_amount: u64,
        input_reserve: u64,
        output_reserve: u64,
        max_price_impact_bp: Option<u16>,
    ) -> PoolResult<()> {
        if input_amount == 0 {
            return Err(PoolError::CalculationError("Input amount cannot be zero".to_string()));
        }

        if input_reserve == 0 || output_reserve == 0 {
            return Err(PoolError::CalculationError("Reserves cannot be zero".to_string()));
        }

        if input_amount > input_reserve {
            return Err(PoolError::CalculationError("Input amount exceeds reserve".to_string()));
        }

        // 检查价格影响
        if let Some(max_impact_bp) = max_price_impact_bp {
            let price_before = output_reserve as f64 / input_reserve as f64;
            let new_input_reserve = input_reserve + input_amount;
            let k = input_reserve as u128 * output_reserve as u128;
            let new_output_reserve = k / new_input_reserve as u128;
            let price_after = new_output_reserve as f64 / new_input_reserve as f64;

            let price_impact = price::calculate_price_impact(price_before, price_after);
            let max_impact = max_impact_bp as f64 / 100.0;

            if price_impact > max_impact {
                return Err(PoolError::CalculationError(
                    format!("Price impact {:.2}% exceeds maximum {:.2}%", price_impact, max_impact)
                ));
            }
        }

        Ok(())
    }
}

/// 费用计算工具
pub mod fees {
    use super::*;

    /// 计算交换费用
    pub fn calculate_swap_fee(
        input_amount: u64,
        fee_rate_bp: u16,
    ) -> u64 {
        (input_amount as u128 * fee_rate_bp as u128 / constants::BASIS_POINT_MAX as u128) as u64
    }

    /// 计算协议费用
    pub fn calculate_protocol_fee(
        total_fee: u64,
        protocol_fee_rate_bp: u16,
    ) -> u64 {
        (total_fee as u128 * protocol_fee_rate_bp as u128 / constants::BASIS_POINT_MAX as u128) as u64
    }

    /// 计算年化收益率 (APY)
    pub fn calculate_apy(
        fee_24h: f64,
        liquidity_value: f64,
    ) -> f64 {
        if liquidity_value == 0.0 {
            return 0.0;
        }

        let daily_yield = fee_24h / liquidity_value;
        ((1.0 + daily_yield).powf(365.0) - 1.0) * 100.0
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_q64_conversion() {
        let price = 1.5;
        let q64_price = q64::from_float(price);
        let converted_back = q64::to_float(q64_price);

        assert!((converted_back - price).abs() < 1e-10);
        assert!(q64::is_valid(q64_price));
    }

    #[test]
    fn test_price_calculations() {
        let price1 = 100.0;
        let price2 = 150.0;

        let diff = price::calculate_percentage_difference(price1, price2);
        assert!((diff - 50.0).abs() < 1e-10);

        let impact = price::calculate_price_impact(price1, price2);
        assert!((impact - 50.0).abs() < 1e-10);
    }

    #[test]
    fn test_cpmm_swap() {
        let result = swap::calculate_cpmm_output(
            1000,  // input amount
            10000, // input reserve
            20000, // output reserve
            30,    // 0.3% fee
        );

        assert!(result.is_ok());
        let output = result.unwrap();
        assert!(output > 0);
        assert!(output < 2000); // Should be less than naive calculation
    }

    #[test]
    fn test_liquidity_calculations() {
        let liquidity = liquidity::calculate_cpmm_liquidity(
            1000000, // 1 token A (6 decimals)
            2000000000, // 2 token B (9 decimals)
            6,
            9,
        );

        assert!((liquidity - (1.0 * 2.0_f64.sqrt())).abs() < 1e-10);
    }
}
