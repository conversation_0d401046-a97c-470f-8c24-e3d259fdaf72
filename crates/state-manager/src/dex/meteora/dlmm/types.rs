//! Meteora DLMM 类型定义
//!
//! 定义 Meteora DLMM 特有的数据结构和类型

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::BTreeMap;
use shared::{EchoesError, Result};

/// Meteora DLMM 数学常量
pub mod math_constants {
    /// 基础点精度 (10000 = 100%)
    pub const BASIS_POINT_MAX: u64 = 10000;
    /// 最大bin数量
    pub const MAX_BIN_PER_ARRAY: usize = 70;
    /// 价格精度位数
    pub const PRICE_PRECISION_BITS: u32 = 64;
    /// 最大bin ID
    pub const MAX_BIN_ID: i32 = 443636;
    /// 最小bin ID
    pub const MIN_BIN_ID: i32 = -443636;
}

/// Meteora DLMM Bin 数据结构
/// 表示单个价格区间的流动性状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBin {
    /// X代币数量
    pub amount_x: u64,
    /// Y代币数量
    pub amount_y: u64,
    /// 价格 (Q64.64格式)
    pub price: u128,
    /// 流动性供应量
    pub liquidity_supply: u128,
    /// 每代币存储的奖励
    pub reward_per_token_stored: [u128; 2],
    /// X代币每代币存储的费用
    pub fee_amount_x_per_token_stored: u128,
    /// Y代币每代币存储的费用
    pub fee_amount_y_per_token_stored: u128,
    /// X代币输入累计
    pub amount_x_in: u64,
    /// Y代币输入累计
    pub amount_y_in: u64,
}

impl MeteoraLbBin {
    /// 检查bin是否为空
    pub fn is_empty(&self) -> bool {
        self.amount_x == 0 && self.amount_y == 0
    }

    /// 获取bin的总价值（以X代币计价）
    pub fn get_total_value_in_x(&self) -> Result<u64> {
        if self.price == 0 {
            return Ok(self.amount_x);
        }

        // 将Y代币转换为X代币等价值
        let price_float = self.price as f64 / (1u128 << 64) as f64;
        let y_value_in_x = (self.amount_y as f64 / price_float) as u64;

        Ok(self.amount_x + y_value_in_x)
    }

    /// 获取bin的总价值（以Y代币计价）
    pub fn get_total_value_in_y(&self) -> Result<u64> {
        if self.price == 0 {
            return Ok(self.amount_y);
        }

        // 将X代币转换为Y代币等价值
        let price_float = self.price as f64 / (1u128 << 64) as f64;
        let x_value_in_y = (self.amount_x as f64 * price_float) as u64;

        Ok(self.amount_y + x_value_in_y)
    }

    /// 计算在给定输入下的输出量
    pub fn calculate_swap_output(&self, input_amount: u64, input_is_x: bool) -> Result<u64> {
        if input_is_x {
            // X -> Y 交换
            if self.amount_y == 0 {
                return Ok(0);
            }

            let available_x = self.amount_x;
            let available_y = self.amount_y;

            // 简化的恒定乘积计算
            let k = available_x as u128 * available_y as u128;
            let new_x = available_x as u128 + input_amount as u128;
            let new_y = k / new_x;
            let output = available_y as u128 - new_y;

            Ok(output.min(available_y as u128) as u64)
        } else {
            // Y -> X 交换
            if self.amount_x == 0 {
                return Ok(0);
            }

            let available_x = self.amount_x;
            let available_y = self.amount_y;

            // 简化的恒定乘积计算
            let k = available_x as u128 * available_y as u128;
            let new_y = available_y as u128 + input_amount as u128;
            let new_x = k / new_y;
            let output = available_x as u128 - new_x;

            Ok(output.min(available_x as u128) as u64)
        }
    }
}

/// Meteora DLMM Bin Array 数据结构
/// 包含一组连续的价格区间
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBinArray {
    /// 数组索引
    pub index: i64,
    /// 版本号
    pub version: u8,
    /// 填充字段
    pub padding: [u8; 7],
    /// 关联的LB Pair地址
    pub lb_pair: Pubkey,
    /// Bin数组
    pub bins: Vec<MeteoraLbBin>,
}

impl MeteoraLbBinArray {
    /// 获取指定索引的bin
    pub fn get_bin(&self, bin_index: usize) -> Option<&MeteoraLbBin> {
        self.bins.get(bin_index)
    }

    /// 获取所有非空的bin
    pub fn get_active_bins(&self) -> Vec<(usize, &MeteoraLbBin)> {
        self.bins
            .iter()
            .enumerate()
            .filter(|(_, bin)| !bin.is_empty())
            .collect()
    }

    /// 计算数组中的总流动性
    pub fn get_total_liquidity(&self) -> u128 {
        self.bins.iter().map(|bin| bin.liquidity_supply).sum()
    }

    /// 获取价格范围
    pub fn get_price_range(&self) -> Option<(u128, u128)> {
        let active_bins = self.get_active_bins();
        if active_bins.is_empty() {
            return None;
        }

        let min_price = active_bins.iter().map(|(_, bin)| bin.price).min()?;
        let max_price = active_bins.iter().map(|(_, bin)| bin.price).max()?;

        Some((min_price, max_price))
    }
}

/// Meteora DLMM Bin Array Bitmap Extension
/// 用于快速查找活跃的bin数组
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBinArrayBitmapExtension {
    /// 关联的LB Pair地址
    pub lb_pair: Pubkey,
    /// 正向bin数组位图
    pub positive_bin_array_bitmap: [[u64; 8]; 12],
    /// 负向bin数组位图
    pub negative_bin_array_bitmap: [[u64; 8]; 12],
}

impl MeteoraLbBinArrayBitmapExtension {
    /// 检查指定的bin数组索引是否活跃
    pub fn is_bin_array_active(&self, array_index: i64) -> bool {
        if array_index >= 0 {
            self.check_positive_bitmap(array_index as u64)
        } else {
            self.check_negative_bitmap((-array_index) as u64)
        }
    }

    /// 检查正向位图
    fn check_positive_bitmap(&self, index: u64) -> bool {
        let word_index = (index / 64) as usize;
        let bit_index = index % 64;

        if word_index >= 12 * 8 {
            return false;
        }

        let array_index = word_index / 8;
        let inner_index = word_index % 8;

        if array_index < 12 && inner_index < 8 {
            (self.positive_bin_array_bitmap[array_index][inner_index] & (1u64 << bit_index)) != 0
        } else {
            false
        }
    }

    /// 检查负向位图
    fn check_negative_bitmap(&self, index: u64) -> bool {
        let word_index = (index / 64) as usize;
        let bit_index = index % 64;

        if word_index >= 12 * 8 {
            return false;
        }

        let array_index = word_index / 8;
        let inner_index = word_index % 8;

        if array_index < 12 && inner_index < 8 {
            (self.negative_bin_array_bitmap[array_index][inner_index] & (1u64 << bit_index)) != 0
        } else {
            false
        }
    }

    /// 获取所有活跃的bin数组索引
    pub fn get_active_bin_array_indices(&self) -> Vec<i64> {
        let mut indices = Vec::new();

        // 检查正向位图
        for array_idx in 0..12 {
            for inner_idx in 0..8 {
                let bitmap = self.positive_bin_array_bitmap[array_idx][inner_idx];
                if bitmap != 0 {
                    for bit in 0..64 {
                        if (bitmap & (1u64 << bit)) != 0 {
                            let index = (array_idx * 8 + inner_idx) * 64 + bit;
                            indices.push(index as i64);
                        }
                    }
                }
            }
        }

        // 检查负向位图
        for array_idx in 0..12 {
            for inner_idx in 0..8 {
                let bitmap = self.negative_bin_array_bitmap[array_idx][inner_idx];
                if bitmap != 0 {
                    for bit in 0..64 {
                        if (bitmap & (1u64 << bit)) != 0 {
                            let index = (array_idx * 8 + inner_idx) * 64 + bit;
                            indices.push(-(index as i64));
                        }
                    }
                }
            }
        }

        indices.sort();
        indices
    }
}

/// Meteora 奖励信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraRewardInfo {
    /// 奖励代币铸币地址
    pub mint: Pubkey,
    /// 奖励库地址
    pub vault: Pubkey,
    /// 权限地址
    pub authority: Pubkey,
    /// 每秒发放量
    pub emissions_per_second: u64,
    /// 累计奖励（每流动性）
    pub reward_per_token_complete: u128,
    /// 待处理奖励
    pub reward_pending: u64,
    /// 最后更新时间
    pub last_update_time: u64,
}

/// Meteora Oracle 信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraOracle {
    /// 样本生命周期
    pub sample_lifetime: u64,
    /// 大小
    pub size: u64,
    /// 权重
    pub weight: u64,
    /// 索引
    pub index: u64,
    /// 最后更新时间
    pub last_update_time: u64,
}

/// 交换估算结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapEstimation {
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 费用金额
    pub fee_amount: u64,
    /// 价格影响（百分比）
    pub price_impact: f64,
    /// 使用的bin信息
    pub bins_used: Vec<BinUsage>,
    /// 剩余未处理的输入
    pub remaining_input: u64,
}

/// Bin使用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinUsage {
    /// Bin ID
    pub bin_id: i32,
    /// 在此bin中的输入金额
    pub input_amount: u64,
    /// 在此bin中的输出金额
    pub output_amount: u64,
    /// Bin价格
    pub price: u128,
    /// 在此bin中的费用
    pub fee: u64,
}

/// 流动性信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityInfo {
    /// 总流动性
    pub total_liquidity: u128,
    /// 活跃bin数量
    pub active_bin_count: usize,
    /// 价格范围 (最小价格, 最大价格)
    pub price_range: (u128, u128),
    /// 流动性分布 (bin_id, price, liquidity)
    pub distribution: Vec<(i32, u128, u128)>,
}

/// 价格信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 当前价格
    pub current_price: f64,
    /// 活跃bin ID
    pub active_bin_id: i32,
    /// 价格精度调整后的价格
    pub adjusted_price: f64,
    /// 价格变化（相对于上次更新）
    pub price_change: Option<f64>,
}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// 总池数量
    pub total_pools: usize,
    /// 总流动性
    pub total_liquidity: u128,
    /// 活跃池数量
    pub active_pools: usize,
}

/// 工具函数
pub mod utils {
    use super::*;

    /// 计算两个价格之间的百分比差异
    pub fn calculate_price_difference(price1: u128, price2: u128) -> f64 {
        if price1 == 0 || price2 == 0 {
            return 0.0;
        }

        let diff = if price1 > price2 {
            price1 - price2
        } else {
            price2 - price1
        };

        (diff as f64 / price1 as f64) * 100.0
    }

    /// 将Q64.64格式的价格转换为浮点数
    pub fn q64_to_float(q64_price: u128) -> f64 {
        q64_price as f64 / (1u128 << 64) as f64
    }

    /// 将浮点数价格转换为Q64.64格式
    pub fn float_to_q64(price: f64) -> u128 {
        (price * (1u128 << 64) as f64) as u128
    }

    /// 格式化流动性数量为可读字符串
    pub fn format_liquidity(liquidity: u128) -> String {
        if liquidity >= 1_000_000_000_000 {
            format!("{:.2}T", liquidity as f64 / 1_000_000_000_000.0)
        } else if liquidity >= 1_000_000_000 {
            format!("{:.2}B", liquidity as f64 / 1_000_000_000.0)
        } else if liquidity >= 1_000_000 {
            format!("{:.2}M", liquidity as f64 / 1_000_000.0)
        } else if liquidity >= 1_000 {
            format!("{:.2}K", liquidity as f64 / 1_000.0)
        } else {
            liquidity.to_string()
        }
    }

    /// 从JSON文件加载Meteora池数据
    pub async fn load_pool_from_json_files(
        address: Pubkey,
        lb_pair_file: &str,
        bin_array_files: &[&str],
        bitmap_file: Option<&str>,
    ) -> Result<crate::dex::meteora::dlmm::MeteoraLbPoolManager> {
        use tokio::fs;

        // 读取LB Pair数据
        let lb_pair_data = fs::read_to_string(lb_pair_file).await
            .map_err(|e| EchoesError::Io(format!("Failed to read LB pair file: {}", e)))?;

        // 读取Bin Array数据
        let mut bin_arrays_data = Vec::new();
        for file in bin_array_files {
            let data = fs::read_to_string(file).await
                .map_err(|e| EchoesError::Io(format!("Failed to read bin array file: {}", e)))?;
            bin_arrays_data.push(data);
        }

        // 读取Bitmap数据（如果提供）
        let bitmap_data = if let Some(file) = bitmap_file {
            Some(fs::read_to_string(file).await
                .map_err(|e| EchoesError::Io(format!("Failed to read bitmap file: {}", e)))?)
        } else {
            None
        };

        // 转换为字符串引用
        let bin_array_refs: Vec<&str> = bin_arrays_data.iter().map(|s| s.as_str()).collect();
        let bitmap_ref = bitmap_data.as_ref().map(|s| s.as_str());

        crate::dex::meteora::dlmm::MeteoraLbPoolManager::from_json_data(address, &lb_pair_data, &bin_array_refs, bitmap_ref)
    }
}

/// Meteora DLMM 交换路径
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbSwapPath {
    /// 使用的 Bin
    pub bins_used: Vec<MeteoraLbBinUsage>,
    /// 总输入
    pub total_input: u64,
    /// 总输出
    pub total_output: u64,
    /// 总费用
    pub total_fee: u64,
    /// 价格影响
    pub price_impact: f64,
}

/// Meteora DLMM Bin 使用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbBinUsage {
    /// Bin ID
    pub bin_id: i32,
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 费用
    pub fee: u64,
    /// Bin 价格
    pub price: u128,
}

/// Meteora DLMM 位置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbPositionInfo {
    /// 位置 NFT 铸币地址
    pub nft_mint: Pubkey,
    /// LB Pair 地址
    pub lb_pair: Pubkey,
    /// 下边界 Bin ID
    pub lower_bin_id: i32,
    /// 上边界 Bin ID
    pub upper_bin_id: i32,
    /// 最后更新时间
    pub last_updated_at: u64,
    /// 总流动性
    pub total_liquidity: u128,
}

/// Meteora DLMM 统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbStats {
    /// 总流动性
    pub total_liquidity: u128,
    /// 活跃 Bin 数量
    pub active_bin_count: usize,
    /// 价格范围
    pub price_range: (u128, u128),
    /// 24小时交易量
    pub volume_24h: u64,
    /// 24小时费用
    pub fees_24h: u64,
}

/// Meteora DLMM 错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MeteoraLbError {
    /// 无效的 Bin ID
    InvalidBinId,
    /// 无效的 Bin 步长
    InvalidBinStep,
    /// 无效的流动性
    InvalidLiquidity,
    /// 无效的价格
    InvalidPrice,
    /// Bin 不存在
    BinNotFound,
    /// Bin 数组不存在
    BinArrayNotFound,
    /// 位置不存在
    PositionNotFound,
    /// 计算溢出
    CalculationOverflow,
    /// 流动性不足
    InsufficientLiquidity,
}

impl std::fmt::Display for MeteoraLbError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MeteoraLbError::InvalidBinId => write!(f, "Invalid bin ID"),
            MeteoraLbError::InvalidBinStep => write!(f, "Invalid bin step"),
            MeteoraLbError::InvalidLiquidity => write!(f, "Invalid liquidity"),
            MeteoraLbError::InvalidPrice => write!(f, "Invalid price"),
            MeteoraLbError::BinNotFound => write!(f, "Bin not found"),
            MeteoraLbError::BinArrayNotFound => write!(f, "Bin array not found"),
            MeteoraLbError::PositionNotFound => write!(f, "Position not found"),
            MeteoraLbError::CalculationOverflow => write!(f, "Calculation overflow"),
            MeteoraLbError::InsufficientLiquidity => write!(f, "Insufficient liquidity"),
        }
    }
}

impl std::error::Error for MeteoraLbError {}

/// Meteora DLMM 结果类型
pub type MeteoraLbResult<T> = std::result::Result<T, MeteoraLbError>;
