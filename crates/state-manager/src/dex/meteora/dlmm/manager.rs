//! Meteora DLMM 池管理器实现
//!
//! 实现 Meteora DLMM 特定的池管理逻辑

use super::types::*;
use super::pool::MeteoraLbPairState;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use shared::{EchoesError, Result};

/// Meteora DLMM 池管理器
/// 负责管理DLMM池的状态和计算交换
#[derive(Debug, Clone)]
pub struct MeteoraLbPoolManager {
    /// 池状态
    pub pool_state: MeteoraLbPairState,
}

impl MeteoraLbPoolManager {
    /// 创建新的池管理器
    pub fn new(pool_state: MeteoraLbPairState) -> Self {
        Self { pool_state }
    }

    /// 从JSON数据创建池管理器
    pub fn from_json_data(
        address: Pubkey,
        lb_pair_data: &str,
        bin_arrays_data: &[&str],
        bitmap_data: Option<&str>,
    ) -> Result<Self> {
        // 解析LB Pair数据
        let lb_pair_json: serde_json::Value = serde_json::from_str(lb_pair_data)
            .map_err(|e| EchoesError::Parse(format!("Failed to parse LB pair data: {}", e)))?;

        // 提取基本信息（这里需要根据实际JSON结构调整）
        let parsed_data = &lb_pair_json["parsed"]["data"];

        // 创建基础池状态
        let mut pool_state = MeteoraLbPairState::new(
            address,
            Pubkey::from_str("11111111111111111111111111111111").unwrap(), // 占位符
            Pubkey::from_str("11111111111111111111111111111111").unwrap(), // 占位符
            6, // 默认精度
            9, // 默认精度
            0, // 默认活跃ID
            1, // 默认bin步长
        );

        // 解析bin数组数据
        for bin_array_data in bin_arrays_data {
            let bin_array_json: serde_json::Value = serde_json::from_str(bin_array_data)
                .map_err(|e| EchoesError::Parse(format!("Failed to parse bin array data: {}", e)))?;

            let parsed_bin_data = &bin_array_json["parsed"]["data"];
            let array_index = parsed_bin_data["index"].as_str()
                .and_then(|s| s.parse::<i64>().ok())
                .unwrap_or(0);

            let lb_pair_str = parsed_bin_data["lbPair"].as_str().unwrap_or("");
            let lb_pair = Pubkey::from_str(lb_pair_str)
                .unwrap_or_else(|_| Pubkey::from_str("11111111111111111111111111111111").unwrap());

            let mut bins = Vec::new();
            if let Some(bins_array) = parsed_bin_data["bins"].as_array() {
                for bin_data in bins_array {
                    let bin = MeteoraLbBin {
                        amount_x: bin_data["amountX"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        amount_y: bin_data["amountY"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        price: bin_data["price"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        liquidity_supply: bin_data["liquiditySupply"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        reward_per_token_stored: [
                            bin_data["rewardPerTokenStored"][0].as_str()
                                .and_then(|s| s.parse().ok()).unwrap_or(0),
                            bin_data["rewardPerTokenStored"][1].as_str()
                                .and_then(|s| s.parse().ok()).unwrap_or(0),
                        ],
                        fee_amount_x_per_token_stored: bin_data["feeAmountXPerTokenStored"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        fee_amount_y_per_token_stored: bin_data["feeAmountYPerTokenStored"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        amount_x_in: bin_data["amountXIn"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                        amount_y_in: bin_data["amountYIn"].as_str()
                            .and_then(|s| s.parse().ok()).unwrap_or(0),
                    };
                    bins.push(bin);
                }
            }

            let bin_array = MeteoraLbBinArray {
                index: array_index,
                version: parsed_bin_data["version"].as_u64().unwrap_or(1) as u8,
                padding: [0; 7],
                lb_pair,
                bins,
            };

            pool_state.update_bin_array(array_index, bin_array);
        }

        // 解析位图数据（如果提供）
        if let Some(bitmap_data) = bitmap_data {
            let bitmap_json: serde_json::Value = serde_json::from_str(bitmap_data)
                .map_err(|e| EchoesError::Parse(format!("Failed to parse bitmap data: {}", e)))?;

            let parsed_bitmap_data = &bitmap_json["parsed"]["data"];
            let lb_pair_str = parsed_bitmap_data["lbPair"].as_str().unwrap_or("");
            let lb_pair = Pubkey::from_str(lb_pair_str)
                .unwrap_or_else(|_| Pubkey::from_str("11111111111111111111111111111111").unwrap());

            // 解析位图数组（简化处理）
            let positive_bitmap = [[0u64; 8]; 12];
            let negative_bitmap = [[0u64; 8]; 12];

            let bitmap_extension = MeteoraLbBinArrayBitmapExtension {
                lb_pair,
                positive_bin_array_bitmap: positive_bitmap,
                negative_bin_array_bitmap: negative_bitmap,
            };

            pool_state.bitmap_extension = Some(bitmap_extension);
        }

        Ok(Self::new(pool_state))
    }

    /// 估算交换输出
    pub fn estimate_swap_output(
        &self,
        input_amount: u64,
        input_is_x: bool,
        max_price_impact: Option<f64>,
    ) -> Result<SwapEstimation> {
        if input_amount == 0 {
            return Err(EchoesError::InvalidInput("Input amount cannot be zero".to_string()));
        }

        let mut remaining_input = input_amount;
        let mut total_output = 0u64;
        let mut bins_used = Vec::new();
        let mut total_fee = 0u64;

        // 获取活跃的bin数组，按价格排序
        let active_arrays = self.pool_state.get_active_bin_arrays();

        // 根据交换方向确定遍历顺序
        let sorted_bins = if input_is_x {
            // X -> Y: 从高价格到低价格
            self.get_sorted_bins_for_x_to_y(&active_arrays)
        } else {
            // Y -> X: 从低价格到高价格
            self.get_sorted_bins_for_y_to_x(&active_arrays)
        };

        let initial_price = self.pool_state.get_active_bin_price()?;
        let mut current_price = initial_price;

        for (bin_id, bin) in sorted_bins {
            if remaining_input == 0 {
                break;
            }

            // 计算这个bin能处理的最大输入量
            let available_liquidity = if input_is_x {
                bin.amount_y
            } else {
                bin.amount_x
            };

            if available_liquidity == 0 {
                continue;
            }

            // 计算在这个bin中的交换
            let bin_input = remaining_input.min(available_liquidity);
            let bin_output = bin.calculate_swap_output(bin_input, input_is_x)?;

            if bin_output > 0 {
                // 计算费用
                let fee = (bin_input as u128 * self.pool_state.base_fee_rate as u128 / 10000) as u64;

                total_output += bin_output;
                total_fee += fee;
                remaining_input -= bin_input;
                current_price = bin.price;

                bins_used.push(BinUsage {
                    bin_id,
                    input_amount: bin_input,
                    output_amount: bin_output,
                    price: bin.price,
                    fee,
                });
            }
        }

        // 计算价格影响
        let price_impact = if initial_price > 0 {
            let price_change = if current_price > initial_price {
                (current_price - initial_price) as f64 / initial_price as f64
            } else {
                (initial_price - current_price) as f64 / initial_price as f64
            };
            price_change * 100.0 // 转换为百分比
        } else {
            0.0
        };

        // 检查价格影响限制
        if let Some(max_impact) = max_price_impact {
            if price_impact > max_impact {
                return Err(EchoesError::InvalidState(
                    format!("Price impact {:.2}% exceeds maximum {:.2}%", price_impact, max_impact)
                ));
            }
        }

        Ok(SwapEstimation {
            input_amount,
            output_amount: total_output,
            fee_amount: total_fee,
            price_impact,
            bins_used,
            remaining_input,
        })
    }

    /// 获取X->Y交换的排序bin列表
    fn get_sorted_bins_for_x_to_y<'a>(&self, active_arrays: &'a [(i64, &'a MeteoraLbBinArray)]) -> Vec<(i32, &'a MeteoraLbBin)> {
        let mut bins = Vec::new();

        for (array_index, bin_array) in active_arrays {
            for (bin_index, bin) in bin_array.bins.iter().enumerate() {
                if !bin.is_empty() && bin.amount_y > 0 {
                    let bin_id = *array_index * math_constants::MAX_BIN_PER_ARRAY as i64 + bin_index as i64;
                    bins.push((bin_id as i32, bin));
                }
            }
        }

        // 按价格从高到低排序（X->Y交换）
        bins.sort_by(|a, b| b.1.price.cmp(&a.1.price));
        bins
    }

    /// 获取Y->X交换的排序bin列表
    fn get_sorted_bins_for_y_to_x<'a>(&self, active_arrays: &'a [(i64, &'a MeteoraLbBinArray)]) -> Vec<(i32, &'a MeteoraLbBin)> {
        let mut bins = Vec::new();

        for (array_index, bin_array) in active_arrays {
            for (bin_index, bin) in bin_array.bins.iter().enumerate() {
                if !bin.is_empty() && bin.amount_x > 0 {
                    let bin_id = *array_index * math_constants::MAX_BIN_PER_ARRAY as i64 + bin_index as i64;
                    bins.push((bin_id as i32, bin));
                }
            }
        }

        // 按价格从低到高排序（Y->X交换）
        bins.sort_by(|a, b| a.1.price.cmp(&b.1.price));
        bins
    }

    /// 获取当前价格
    pub fn get_current_price(&self) -> Result<f64> {
        let price_q64 = self.pool_state.get_active_bin_price()?;
        let price_float = price_q64 as f64 / (1u128 << 64) as f64;

        // 调整小数位差异
        let decimals_diff = self.pool_state.token_y_decimals as i32 - self.pool_state.token_x_decimals as i32;
        let adjusted_price = price_float * 10f64.powi(decimals_diff);

        Ok(adjusted_price)
    }

    /// 更新池状态
    pub fn update_pool_state(&mut self, new_state: MeteoraLbPairState) {
        self.pool_state = new_state;
    }

    /// 获取流动性分布信息
    pub fn get_liquidity_info(&self) -> LiquidityInfo {
        let distribution = self.pool_state.get_liquidity_distribution();
        let total_liquidity = self.pool_state.get_total_liquidity();
        let active_bin_count = distribution.len();

        let (min_price, max_price) = if distribution.is_empty() {
            (0, 0)
        } else {
            let min = distribution.iter().map(|(_, price, _)| *price).min().unwrap_or(0);
            let max = distribution.iter().map(|(_, price, _)| *price).max().unwrap_or(0);
            (min, max)
        };

        LiquidityInfo {
            total_liquidity,
            active_bin_count,
            price_range: (min_price, max_price),
            distribution,
        }
    }
}

/// Meteora DLMM 池状态缓存管理器
#[derive(Debug)]
pub struct MeteoraLbPoolCache {
    /// 池管理器映射
    pools: std::sync::Arc<std::sync::RwLock<std::collections::HashMap<Pubkey, MeteoraLbPoolManager>>>,
}

impl Default for MeteoraLbPoolCache {
    fn default() -> Self {
        Self::new()
    }
}

impl MeteoraLbPoolCache {
    /// 创建新的缓存管理器
    pub fn new() -> Self {
        Self {
            pools: std::sync::Arc::new(std::sync::RwLock::new(std::collections::HashMap::new())),
        }
    }

    /// 添加或更新池
    pub fn upsert_pool(&self, address: Pubkey, manager: MeteoraLbPoolManager) -> Result<()> {
        let mut pools = self.pools.write().unwrap();
        pools.insert(address, manager);
        Ok(())
    }

    /// 获取池管理器
    pub fn get_pool(&self, address: &Pubkey) -> Option<MeteoraLbPoolManager> {
        let pools = self.pools.read().unwrap();
        pools.get(address).cloned()
    }

    /// 移除池
    pub fn remove_pool(&self, address: &Pubkey) -> Option<MeteoraLbPoolManager> {
        let mut pools = self.pools.write().unwrap();
        pools.remove(address)
    }

    /// 获取所有池地址
    pub fn get_all_pool_addresses(&self) -> Vec<Pubkey> {
        let pools = self.pools.read().unwrap();
        pools.keys().cloned().collect()
    }

    /// 根据代币对查找池
    pub fn find_pools_by_tokens(&self, token_x: &Pubkey, token_y: &Pubkey) -> Vec<(Pubkey, MeteoraLbPoolManager)> {
        let pools = self.pools.read().unwrap();
        pools
            .iter()
            .filter(|(_, manager)| {
                let state = &manager.pool_state;
                (state.token_x_mint == *token_x && state.token_y_mint == *token_y) ||
                (state.token_x_mint == *token_y && state.token_y_mint == *token_x)
            })
            .map(|(&addr, manager)| (addr, manager.clone()))
            .collect()
    }

    /// 清空缓存
    pub fn clear(&self) {
        let mut pools = self.pools.write().unwrap();
        pools.clear();
    }

    /// 获取缓存统计
    pub fn get_stats(&self) -> CacheStats {
        let pools = self.pools.read().unwrap();
        let total_pools = pools.len();
        let total_liquidity: u128 = pools.values()
            .map(|manager| manager.pool_state.get_total_liquidity())
            .sum();

        CacheStats {
            total_pools,
            total_liquidity,
            active_pools: pools.values()
                .filter(|manager| manager.pool_state.get_total_liquidity() > 0)
                .count(),
        }
    }
}
