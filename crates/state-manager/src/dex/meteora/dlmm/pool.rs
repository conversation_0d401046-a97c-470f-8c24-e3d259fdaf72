//! Meteora DLMM 池状态实现
//!
//! 实现 Meteora DLMM 特定的池状态逻辑

use super::types::*;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::BTreeMap;
use std::str::FromStr;
use shared::{EchoesError, Result};

/// Meteora DLMM LB Pair 主要状态
/// 表示一个完整的DLMM流动性池
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraLbPairState {
    /// 池地址
    pub address: Pubkey,
    /// X代币铸币地址
    pub token_x_mint: Pubkey,
    /// Y代币铸币地址
    pub token_y_mint: Pubkey,
    /// X代币小数位数
    pub token_x_decimals: u8,
    /// Y代币小数位数
    pub token_y_decimals: u8,
    /// 当前活跃的bin ID
    pub active_id: i32,
    /// bin步长
    pub bin_step: u16,
    /// 基础费率（基点）
    pub base_fee_rate: u16,
    /// 最大费率（基点）
    pub max_fee_rate: u16,
    /// 协议费率（基点）
    pub protocol_fee_rate: u16,
    /// 流动性挖矿费率（基点）
    pub liquidity_mining_fee_rate: u16,
    /// 总费用X
    pub total_fee_x: u64,
    /// 总费用Y
    pub total_fee_y: u64,
    /// 协议费用X
    pub protocol_fee_x: u64,
    /// 协议费用Y
    pub protocol_fee_y: u64,
    /// 累计费用X（每流动性）
    pub fee_x_per_token_complete: u128,
    /// 累计费用Y（每流动性）
    pub fee_y_per_token_complete: u128,
    /// 累计费用X（待处理）
    pub fee_x_pending: u64,
    /// 累计费用Y（待处理）
    pub fee_y_pending: u64,
    /// 奖励信息
    pub reward_infos: Vec<MeteoraRewardInfo>,
    /// Oracle信息
    pub oracle: MeteoraOracle,
    /// Bin数组映射 (array_index -> BinArray)
    pub bin_arrays: BTreeMap<i64, MeteoraLbBinArray>,
    /// 位图扩展
    pub bitmap_extension: Option<MeteoraLbBinArrayBitmapExtension>,
    /// 最后更新时间戳
    pub last_updated_slot: u64,
}

impl MeteoraLbPairState {
    /// 创建新的LB Pair状态
    pub fn new(
        address: Pubkey,
        token_x_mint: Pubkey,
        token_y_mint: Pubkey,
        token_x_decimals: u8,
        token_y_decimals: u8,
        active_id: i32,
        bin_step: u16,
    ) -> Self {
        Self {
            address,
            token_x_mint,
            token_y_mint,
            token_x_decimals,
            token_y_decimals,
            active_id,
            bin_step,
            base_fee_rate: 0,
            max_fee_rate: 0,
            protocol_fee_rate: 0,
            liquidity_mining_fee_rate: 0,
            total_fee_x: 0,
            total_fee_y: 0,
            protocol_fee_x: 0,
            protocol_fee_y: 0,
            fee_x_per_token_complete: 0,
            fee_y_per_token_complete: 0,
            fee_x_pending: 0,
            fee_y_pending: 0,
            reward_infos: Vec::new(),
            oracle: MeteoraOracle {
                sample_lifetime: 0,
                size: 0,
                weight: 0,
                index: 0,
                last_update_time: 0,
            },
            bin_arrays: BTreeMap::new(),
            bitmap_extension: None,
            last_updated_slot: 0,
        }
    }

    /// 获取当前活跃bin的价格
    pub fn get_active_bin_price(&self) -> Result<u128> {
        // 计算bin ID对应的价格
        // price = (1 + bin_step / 10000)^bin_id
        let bin_step_decimal = self.bin_step as f64 / 10000.0;
        let base = 1.0 + bin_step_decimal;
        let price_float = base.powf(self.active_id as f64);

        // 转换为Q64.64格式
        let price_q64 = (price_float * (1u128 << 64) as f64) as u128;
        Ok(price_q64)
    }

    /// 获取指定bin ID的价格
    pub fn get_bin_price(&self, bin_id: i32) -> Result<u128> {
        let bin_step_decimal = self.bin_step as f64 / 10000.0;
        let base = 1.0 + bin_step_decimal;
        let price_float = base.powf(bin_id as f64);

        // 转换为Q64.64格式
        let price_q64 = (price_float * (1u128 << 64) as f64) as u128;
        Ok(price_q64)
    }

    /// 添加或更新bin数组
    pub fn update_bin_array(&mut self, array_index: i64, bin_array: MeteoraLbBinArray) {
        self.bin_arrays.insert(array_index, bin_array);
    }

    /// 获取指定数组索引的bin数组
    pub fn get_bin_array(&self, array_index: i64) -> Option<&MeteoraLbBinArray> {
        self.bin_arrays.get(&array_index)
    }

    /// 获取所有活跃的bin数组
    pub fn get_active_bin_arrays(&self) -> Vec<(i64, &MeteoraLbBinArray)> {
        if let Some(bitmap) = &self.bitmap_extension {
            let active_indices = bitmap.get_active_bin_array_indices();
            active_indices
                .iter()
                .filter_map(|&index| {
                    self.bin_arrays.get(&index).map(|array| (index, array))
                })
                .collect()
        } else {
            // 如果没有位图，返回所有非空的bin数组
            self.bin_arrays
                .iter()
                .filter(|(_, array)| array.get_total_liquidity() > 0)
                .map(|(&index, array)| (index, array))
                .collect()
        }
    }

    /// 计算总流动性
    pub fn get_total_liquidity(&self) -> u128 {
        self.bin_arrays
            .values()
            .map(|array| array.get_total_liquidity())
            .sum()
    }

    /// 获取流动性分布
    pub fn get_liquidity_distribution(&self) -> Vec<(i32, u128, u128)> {
        let mut distribution = Vec::new();

        for (&array_index, bin_array) in &self.bin_arrays {
            for (bin_index, bin) in bin_array.bins.iter().enumerate() {
                if !bin.is_empty() {
                    // 计算实际的bin ID
                    let bin_id = array_index * math_constants::MAX_BIN_PER_ARRAY as i64 + bin_index as i64;
                    distribution.push((bin_id as i32, bin.price, bin.liquidity_supply));
                }
            }
        }

        distribution.sort_by_key(|(bin_id, _, _)| *bin_id);
        distribution
    }
}




