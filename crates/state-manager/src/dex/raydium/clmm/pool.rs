//! Raydium CLMM 池状态实现
//!
//! 实现 Raydium CLMM 特定的池状态逻辑

use super::types::*;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use shared::{EchoesError, Result};

/// Raydium CLMM 池状态 - 基于真实链上数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RaydiumClmmPoolState {
    /// 池地址
    pub pool_id: Pubkey,
    /// AMM配置地址
    pub amm_config: Pubkey,
    /// 池所有者
    pub owner: Pubkey,
    /// Token 0 (base token) 铸币地址
    pub token_mint_0: Pubkey,
    /// Token 1 (quote token) 铸币地址
    pub token_mint_1: Pubkey,
    /// Token 0 库地址
    pub token_vault_0: Pubkey,
    /// Token 1 库地址
    pub token_vault_1: Pubkey,
    /// 观察状态键
    pub observation_key: Pubkey,
    /// Token 0 小数位数
    pub mint_decimals_0: u8,
    /// Token 1 小数位数
    pub mint_decimals_1: u8,
    /// Tick间距
    pub tick_spacing: u16,
    /// 当前流动性
    pub liquidity: u128,
    /// 当前价格的平方根 (Q64.64格式)
    pub sqrt_price_x64: u128,
    /// 当前tick
    pub tick_current: i32,
    /// 全局费用增长 Token 0 (Q64.64格式)
    pub fee_growth_global_0_x64: u128,
    /// 全局费用增长 Token 1 (Q64.64格式)
    pub fee_growth_global_1_x64: u128,
    /// 协议费用 Token 0
    pub protocol_fees_token_0: u64,
    /// 协议费用 Token 1
    pub protocol_fees_token_1: u64,
    /// 交换输入量 Token 0
    pub swap_in_amount_token_0: u128,
    /// 交换输出量 Token 1
    pub swap_out_amount_token_1: u128,
    /// 交换输入量 Token 1
    pub swap_in_amount_token_1: u128,
    /// 交换输出量 Token 0
    pub swap_out_amount_token_0: u128,
    /// 池状态
    pub status: u8,
    /// 奖励信息
    pub reward_infos: Vec<RewardInfo>,
    /// Tick数组位图
    pub tick_array_bitmap: [u64; 16],
    /// 总费用 Token 0
    pub total_fees_token_0: u64,
    /// 已领取费用 Token 0
    pub total_fees_claimed_token_0: u64,
    /// 总费用 Token 1
    pub total_fees_token_1: u64,
    /// 已领取费用 Token 1
    pub total_fees_claimed_token_1: u64,
    /// 基金费用 Token 0
    pub fund_fees_token_0: u64,
    /// 基金费用 Token 1
    pub fund_fees_token_1: u64,
    /// 开放时间
    pub open_time: u64,
    /// 最近周期
    pub recent_epoch: u64,
    pub fee_rate: i32,
    pub active: bool,
}

// JSON数据解析辅助函数
impl RaydiumClmmPoolState {
    /// 从JSON数据创建池状态
    pub fn from_json_value(value: &serde_json::Value) -> Result<Self> {
        let data = value["parsed"]["data"].as_object()
            .ok_or_else(|| EchoesError::Parsing("Invalid pool state JSON structure".to_string()))?;

        Ok(Self {
            pool_id: Pubkey::default(), // 需要从外部提供
            amm_config: Pubkey::from_str(
                data["ammConfig"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing ammConfig".to_string()))?
            ).map_err(|e| EchoesError::Parsing(format!("Invalid ammConfig: {}", e)))?,

            owner: Pubkey::from_str(
                data["owner"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing owner".to_string()))?
            ).map_err(|e| EchoesError::Parsing(format!("Invalid owner: {}", e)))?,

            token_mint_0: Pubkey::from_str(
                data["tokenMint0"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing tokenMint0".to_string()))?
            ).map_err(|e| EchoesError::Parsing(format!("Invalid tokenMint0: {}", e)))?,

            token_mint_1: Pubkey::from_str(
                data["tokenMint1"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing tokenMint1".to_string()))?
            ).map_err(|e| EchoesError::Parsing(format!("Invalid tokenMint1: {}", e)))?,

            token_vault_0: Pubkey::from_str(
                data["tokenVault0"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing tokenVault0".to_string()))?
            ).map_err(|e| EchoesError::Parsing(format!("Invalid tokenVault0: {}", e)))?,

            token_vault_1: Pubkey::from_str(
                data["tokenVault1"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing tokenVault1".to_string()))?
            ).map_err(|e| EchoesError::Parsing(format!("Invalid tokenVault1: {}", e)))?,

            observation_key: Pubkey::from_str(
                data["observationKey"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing observationKey".to_string()))?
            ).map_err(|e| EchoesError::Parsing(format!("Invalid observationKey: {}", e)))?,

            mint_decimals_0: data["mintDecimals0"].as_u64()
                .ok_or_else(|| EchoesError::Parsing("Missing mintDecimals0".to_string()))? as u8,

            mint_decimals_1: data["mintDecimals1"].as_u64()
                .ok_or_else(|| EchoesError::Parsing("Missing mintDecimals1".to_string()))? as u8,

            tick_spacing: data["tickSpacing"].as_u64()
                .ok_or_else(|| EchoesError::Parsing("Missing tickSpacing".to_string()))? as u16,

            liquidity: data["liquidity"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing liquidity".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid liquidity: {}", e)))?,

            sqrt_price_x64: data["sqrtPriceX64"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing sqrtPriceX64".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid sqrtPriceX64: {}", e)))?,

            tick_current: data["tickCurrent"].as_i64()
                .ok_or_else(|| EchoesError::Parsing("Missing tickCurrent".to_string()))? as i32,

            fee_growth_global_0_x64: data["feeGrowthGlobal0X64"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing feeGrowthGlobal0X64".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid feeGrowthGlobal0X64: {}", e)))?,

            fee_growth_global_1_x64: data["feeGrowthGlobal1X64"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing feeGrowthGlobal1X64".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid feeGrowthGlobal1X64: {}", e)))?,

            protocol_fees_token_0: data["protocolFeesToken0"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing protocolFeesToken0".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid protocolFeesToken0: {}", e)))?,

            protocol_fees_token_1: data["protocolFeesToken1"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing protocolFeesToken1".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid protocolFeesToken1: {}", e)))?,

            swap_in_amount_token_0: data["swapInAmountToken0"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing swapInAmountToken0".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid swapInAmountToken0: {}", e)))?,

            swap_out_amount_token_1: data["swapOutAmountToken1"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing swapOutAmountToken1".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid swapOutAmountToken1: {}", e)))?,

            swap_in_amount_token_1: data["swapInAmountToken1"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing swapInAmountToken1".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid swapInAmountToken1: {}", e)))?,

            swap_out_amount_token_0: data["swapOutAmountToken0"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing swapOutAmountToken0".to_string()))?
                .parse::<u128>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid swapOutAmountToken0: {}", e)))?,

            status: data["status"].as_u64()
                .ok_or_else(|| EchoesError::Parsing("Missing status".to_string()))? as u8,

            reward_infos: Self::parse_reward_infos(data)?,

            tick_array_bitmap: Self::parse_tick_array_bitmap(data)?,

            total_fees_token_0: data["totalFeesToken0"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing totalFeesToken0".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid totalFeesToken0: {}", e)))?,

            total_fees_claimed_token_0: data["totalFeesClaimedToken0"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing totalFeesClaimedToken0".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid totalFeesClaimedToken0: {}", e)))?,

            total_fees_token_1: data["totalFeesToken1"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing totalFeesToken1".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid totalFeesToken1: {}", e)))?,

            total_fees_claimed_token_1: data["totalFeesClaimedToken1"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing totalFeesClaimedToken1".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid totalFeesClaimedToken1: {}", e)))?,

            fund_fees_token_0: data["fundFeesToken0"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing fundFeesToken0".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid fundFeesToken0: {}", e)))?,

            fund_fees_token_1: data["fundFeesToken1"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing fundFeesToken1".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid fundFeesToken1: {}", e)))?,

            open_time: data["openTime"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing openTime".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid openTime: {}", e)))?,

            recent_epoch: data["recentEpoch"].as_str()
                .ok_or_else(|| EchoesError::Parsing("Missing recentEpoch".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid recentEpoch: {}", e)))?,
            fee_rate: 0,
            active: false,
        })
    }

    fn parse_reward_infos(data: &serde_json::Map<String, serde_json::Value>) -> Result<Vec<RewardInfo>> {
        let reward_infos_array = data["rewardInfos"].as_array()
            .ok_or_else(|| EchoesError::Parsing("Missing rewardInfos".to_string()))?;

        let mut reward_infos = Vec::new();
        for reward_info_value in reward_infos_array {
            let reward_info_obj = reward_info_value.as_object()
                .ok_or_else(|| EchoesError::Parsing("Invalid reward info structure".to_string()))?;

            let reward_info = RewardInfo {
                reward_state: reward_info_obj["rewardState"].as_u64()
                    .ok_or_else(|| EchoesError::Parsing("Missing rewardState".to_string()))? as u8,

                open_time: reward_info_obj["openTime"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing openTime".to_string()))?
                    .parse::<u64>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid openTime: {}", e)))?,

                end_time: reward_info_obj["endTime"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing endTime".to_string()))?
                    .parse::<u64>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid endTime: {}", e)))?,

                last_update_time: reward_info_obj["lastUpdateTime"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing lastUpdateTime".to_string()))?
                    .parse::<u64>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid lastUpdateTime: {}", e)))?,

                emissions_per_second_x64: reward_info_obj["emissionsPerSecondX64"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing emissionsPerSecondX64".to_string()))?
                    .parse::<u128>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid emissionsPerSecondX64: {}", e)))?,

                reward_total_emissioned: reward_info_obj["rewardTotalEmissioned"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing rewardTotalEmissioned".to_string()))?
                    .parse::<u64>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid rewardTotalEmissioned: {}", e)))?,

                reward_claimed: reward_info_obj["rewardClaimed"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing rewardClaimed".to_string()))?
                    .parse::<u64>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid rewardClaimed: {}", e)))?,

                token_mint: Pubkey::from_str(
                    reward_info_obj["tokenMint"].as_str()
                        .ok_or_else(|| EchoesError::Parsing("Missing tokenMint".to_string()))?
                ).map_err(|e| EchoesError::Parsing(format!("Invalid tokenMint: {}", e)))?,

                token_vault: Pubkey::from_str(
                    reward_info_obj["tokenVault"].as_str()
                        .ok_or_else(|| EchoesError::Parsing("Missing tokenVault".to_string()))?
                ).map_err(|e| EchoesError::Parsing(format!("Invalid tokenVault: {}", e)))?,

                authority: Pubkey::from_str(
                    reward_info_obj["authority"].as_str()
                        .ok_or_else(|| EchoesError::Parsing("Missing authority".to_string()))?
                ).map_err(|e| EchoesError::Parsing(format!("Invalid authority: {}", e)))?,

                reward_growth_global_x64: reward_info_obj["rewardGrowthGlobalX64"].as_str()
                    .ok_or_else(|| EchoesError::Parsing("Missing rewardGrowthGlobalX64".to_string()))?
                    .parse::<u128>()
                    .map_err(|e| EchoesError::Parsing(format!("Invalid rewardGrowthGlobalX64: {}", e)))?,
            };

            reward_infos.push(reward_info);
        }

        Ok(reward_infos)
    }

    fn parse_tick_array_bitmap(data: &serde_json::Map<String, serde_json::Value>) -> Result<[u64; 16]> {
        let bitmap_array = data["tickArrayBitmap"].as_array()
            .ok_or_else(|| EchoesError::Parsing("Missing tickArrayBitmap".to_string()))?;

        if bitmap_array.len() != 16 {
            return Err(EchoesError::Parsing("Invalid tickArrayBitmap length".to_string()));
        }

        let mut bitmap = [0u64; 16];
        for (i, value) in bitmap_array.iter().enumerate() {
            bitmap[i] = value.as_str()
                .ok_or_else(|| EchoesError::Parsing("Invalid bitmap value".to_string()))?
                .parse::<u64>()
                .map_err(|e| EchoesError::Parsing(format!("Invalid bitmap value: {}", e)))?;
        }

        Ok(bitmap)
    }
}




