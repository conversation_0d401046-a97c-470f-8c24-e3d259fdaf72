//! 流动性池状态管理模块
//!
//! 提供高性能的内存池状态缓存和实时更新功能

use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{SystemTime, UNIX_EPOCH};
use shared::{EchoesError, Result};
use crate::raydium_clmm::{RaydiumClmmPoolManager, RaydiumClmmPoolState};
use crate::meteora_dlmm::{MeteoraLbPoolManager, MeteoraLbPairState};

/// 流动性池类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PoolType {
    /// Raydium CLMM 池
    RaydiumClmm,
    /// Raydium CPMM 池
    RaydiumCpmm,
    /// Orca Whirlpool
    OrcaWhirlpool,
    /// Meteora DLMM
    MeteoraLbClmm,
    /// PumpFun 绑定曲线
    PumpFun,
    /// Bonk
    Bonk,
}

/// 代币储备信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenReserve {
    /// 代币铸币地址
    pub mint: Pubkey,
    /// 代币余额
    pub amount: u64,
    /// 代币精度
    pub decimals: u8,
    /// 代币符号（可选）
    pub symbol: Option<String>,
}

/// CLMM 池特定状态 - 通用接口，兼容多种CLMM实现
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClmmPoolState {
    /// 当前价格的平方根（Q64.64格式）
    pub sqrt_price_x64: u128,
    /// 当前tick
    pub tick_current: i32,
    /// 观察状态索引
    pub observation_index: u16,
    /// 总流动性
    pub liquidity: u128,
    /// tick间距
    pub tick_spacing: u16,
    /// 手续费率
    pub fee_rate: u32,
    /// 协议手续费率
    pub protocol_fee_rate: u32,
    /// 全局费用增长 Token 0 (Q64.64格式)
    pub fee_growth_global_0_x64: u128,
    /// 全局费用增长 Token 1 (Q64.64格式)
    pub fee_growth_global_1_x64: u128,
    /// Tick数组位图（用于快速查找已初始化的tick）
    pub tick_array_bitmap: [u64; 16],
    /// 协议费用累计
    pub protocol_fees_token_0: u64,
    pub protocol_fees_token_1: u64,
    /// 总交易量统计
    pub total_fees_token_0: u64,
    pub total_fees_token_1: u64,
    /// 已领取费用
    pub total_fees_claimed_token_0: u64,
    pub total_fees_claimed_token_1: u64,
    /// 奖励信息（支持多个奖励代币）
    pub reward_infos: Vec<ClmmRewardInfo>,
    /// 开放时间
    pub open_time: u64,
    /// 最近周期
    pub recent_epoch: u64,
    /// 专用管理器引用（用于Raydium CLMM的高级功能）
    #[serde(skip)]
    pub raydium_manager: Option<Arc<RwLock<RaydiumClmmPoolManager>>>,
    /// Meteora DLMM管理器引用
    #[serde(skip)]
    pub meteora_manager: Option<Arc<RwLock<MeteoraLbPoolManager>>>,
}

/// CLMM 奖励信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClmmRewardInfo {
    /// 奖励状态 (0: 未激活, 1: 激活, 2: 已结束)
    pub reward_state: u8,
    /// 开始时间
    pub open_time: u64,
    /// 结束时间
    pub end_time: u64,
    /// 最后更新时间
    pub last_update_time: u64,
    /// 每秒发放量 (Q64.64格式)
    pub emissions_per_second_x64: u128,
    /// 总发放量
    pub reward_total_emissioned: u64,
    /// 已领取量
    pub reward_claimed: u64,
    /// 奖励代币铸币地址
    pub token_mint: Pubkey,
    /// 奖励代币库地址
    pub token_vault: Pubkey,
    /// 权限地址
    pub authority: Pubkey,
    /// 全局奖励增长 (Q64.64格式)
    pub reward_growth_global_x64: u128,
}

/// CPMM 池特定状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpmmPoolState {
    /// 恒定乘积 k = x * y
    pub k_value: u128,
    /// 手续费率
    pub fee_rate: u32,
    /// LP代币总供应量
    pub lp_supply: u64,
}

/// PumpFun 绑定曲线状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PumpFunBondingCurveState {
    /// 虚拟SOL储备
    pub virtual_sol_reserves: u64,
    /// 虚拟代币储备
    pub virtual_token_reserves: u64,
    /// 真实SOL储备
    pub real_sol_reserves: u64,
    /// 真实代币储备
    pub real_token_reserves: u64,
    /// 代币总供应量
    pub token_total_supply: u64,
    /// 是否完成
    pub complete: bool,
}

/// 池特定状态枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PoolSpecificState {
    Clmm(ClmmPoolState),
    Cpmm(CpmmPoolState),
    PumpFun(PumpFunBondingCurveState),
    /// Raydium CLMM 专用状态（支持链上真实数据）
    RaydiumClmm(Box<RaydiumClmmPoolState>),
    /// Meteora DLMM 专用状态
    MeteoraLbClmm(Box<MeteoraLbPairState>),
    Generic, // 用于简单的AMM池
}

/// 流动性池状态 - 支持多种DEX协议
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolState {
    /// 池地址
    pub address: Pubkey,
    /// 池类型
    pub pool_type: PoolType,
    /// 代币A储备
    pub token_a: TokenReserve,
    /// 代币B储备
    pub token_b: TokenReserve,
    /// 手续费收集者地址
    pub fee_recipient: Option<Pubkey>,
    /// 池特定状态
    pub specific_state: PoolSpecificState,
    /// 最后更新时间戳（Unix时间戳，毫秒）
    pub last_updated: u64,
    /// 最后更新的槽位
    pub last_slot: u64,
    /// 版本号（用于乐观锁）
    pub version: u64,
    /// 是否活跃
    pub active: bool,
    /// 24小时交易量（以USD计）
    pub volume_24h_usd: f64,
    /// TVL（总锁定价值，以USD计）
    pub tvl_usd: f64,
    /// 扩展字段：用于存储原始链上数据的引用或ID
    pub raw_data_ref: Option<String>,
}

impl PoolState {
    /// 创建新的池状态
    pub fn new(
        address: Pubkey,
        pool_type: PoolType,
        token_a: TokenReserve,
        token_b: TokenReserve,
        specific_state: PoolSpecificState,
    ) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Self {
            address,
            pool_type,
            token_a,
            token_b,
            fee_recipient: None,
            specific_state,
            last_updated: now,
            last_slot: 0,
            version: 0,
            active: true,
            volume_24h_usd: 0.0,
            tvl_usd: 0.0,
            raw_data_ref: None,
        }
    }

    /// 更新代币储备
    pub fn update_reserves(&mut self, token_a_amount: u64, token_b_amount: u64, slot: u64) {
        self.token_a.amount = token_a_amount;
        self.token_b.amount = token_b_amount;
        self.last_slot = slot;
        self.last_updated = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        self.version += 1;
    }

    /// 计算代币A到代币B的价格
    pub fn calculate_price_a_to_b(&self) -> Result<f64> {
        match &self.specific_state {
            PoolSpecificState::Clmm(clmm_state) => {
                // CLMM价格从sqrt_price_x64计算
                let sqrt_price = clmm_state.sqrt_price_x64 as f64 / (1u128 << 64) as f64;
                let price = sqrt_price * sqrt_price;
                Ok(price)
            }
            PoolSpecificState::RaydiumClmm(raydium_state) => {
                // 使用Raydium CLMM专用价格计算（考虑小数位差异）
                let sqrt_price = raydium_state.sqrt_price_x64 as f64 / (1u128 << 64) as f64;
                let price = sqrt_price * sqrt_price;

                // 调整小数位差异
                let decimals_diff = raydium_state.mint_decimals_1 as i32 - raydium_state.mint_decimals_0 as i32;
                let adjusted_price = price * 10f64.powi(decimals_diff);
                Ok(adjusted_price)
            }
            PoolSpecificState::MeteoraLbClmm(meteora_state) => {
                // 使用Meteora DLMM的活跃bin价格
                let price_q64 = meteora_state.get_active_bin_price()
                    .map_err(|e| EchoesError::InvalidState(format!("Failed to get Meteora price: {}", e)))?;
                let price = price_q64 as f64 / (1u128 << 64) as f64;

                // 调整小数位差异
                let decimals_diff = meteora_state.token_y_decimals as i32 - meteora_state.token_x_decimals as i32;
                let adjusted_price = price * 10f64.powi(decimals_diff);
                Ok(adjusted_price)
            }
            PoolSpecificState::Cpmm(_) | PoolSpecificState::Generic => {
                // 简单的恒定乘积公式
                if self.token_a.amount == 0 {
                    return Err(EchoesError::InvalidState("Token A reserve is zero".to_string()));
                }
                let price = self.token_b.amount as f64 / self.token_a.amount as f64;
                // 调整精度差异
                let decimals_diff = self.token_b.decimals as i32 - self.token_a.decimals as i32;
                let adjusted_price = price * 10f64.powi(decimals_diff);
                Ok(adjusted_price)
            }
            PoolSpecificState::PumpFun(pf_state) => {
                if pf_state.virtual_token_reserves == 0 {
                    return Err(EchoesError::InvalidState("Virtual token reserves is zero".to_string()));
                }
                let price = pf_state.virtual_sol_reserves as f64 / pf_state.virtual_token_reserves as f64;
                Ok(price)
            }
        }
    }

    /// 估算交换输出量
    pub fn estimate_swap_output(&self, input_amount: u64, input_is_token_a: bool) -> Result<u64> {
        match &self.specific_state {
            PoolSpecificState::Cpmm(cpmm_state) => {
                let fee_rate = cpmm_state.fee_rate as f64 / 10000.0; // 假设费率以基点表示
                let input_after_fee = input_amount as f64 * (1.0 - fee_rate);

                let (input_reserve, output_reserve) = if input_is_token_a {
                    (self.token_a.amount as f64, self.token_b.amount as f64)
                } else {
                    (self.token_b.amount as f64, self.token_a.amount as f64)
                };

                // 恒定乘积公式: (x + dx) * (y - dy) = x * y
                // dy = y * dx / (x + dx)
                let output = (output_reserve * input_after_fee) / (input_reserve + input_after_fee);
                Ok(output as u64)
            }
            PoolSpecificState::RaydiumClmm(_raydium_state) => {
                // 对于Raydium CLMM，使用简化估算
                // 实际实现应该使用 RaydiumClmmPoolManager 的 estimate_swap_output 方法
                // 这里提供基础的回退实现
                let price = self.calculate_price_a_to_b()?;
                if input_is_token_a {
                    let output = input_amount as f64 * price * 0.997; // 0.3% 费用
                    Ok(output as u64)
                } else {
                    let output = input_amount as f64 / price * 0.997; // 0.3% 费用
                    Ok(output as u64)
                }
            }
            PoolSpecificState::PumpFun(pf_state) => {
                if input_is_token_a {
                    // SOL -> Token
                    let k = pf_state.virtual_sol_reserves as f64 * pf_state.virtual_token_reserves as f64;
                    let new_sol_reserves = pf_state.virtual_sol_reserves as f64 + input_amount as f64;
                    let new_token_reserves = k / new_sol_reserves;
                    let output = pf_state.virtual_token_reserves as f64 - new_token_reserves;
                    Ok(output as u64)
                } else {
                    // Token -> SOL
                    let k = pf_state.virtual_sol_reserves as f64 * pf_state.virtual_token_reserves as f64;
                    let new_token_reserves = pf_state.virtual_token_reserves as f64 + input_amount as f64;
                    let new_sol_reserves = k / new_token_reserves;
                    let output = pf_state.virtual_sol_reserves as f64 - new_sol_reserves;
                    Ok(output as u64)
                }
            }
            PoolSpecificState::MeteoraLbClmm(_meteora_state) => {
                // 对于Meteora DLMM，使用简化估算
                // 实际实现应该使用 MeteoraLbPoolManager 的 estimate_swap_output 方法
                let price = self.calculate_price_a_to_b()?;
                if input_is_token_a {
                    let output = input_amount as f64 * price * 0.997; // 假设0.3%费用
                    Ok(output as u64)
                } else {
                    let output = input_amount as f64 / price * 0.997; // 假设0.3%费用
                    Ok(output as u64)
                }
            }
            _ => Err(EchoesError::InvalidState(
                "Swap estimation not implemented for this pool type".to_string(),
            )),
        }
    }

    /// 检查状态是否过期
    pub fn is_stale(&self, max_age_ms: u64) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        now - self.last_updated > max_age_ms
    }

    /// 获取池的唯一标识符
    pub fn get_pool_key(&self) -> String {
        format!("{}:{}", self.pool_type as u8, self.address)
    }

    /// 检查是否为Raydium CLMM池
    pub fn is_raydium_clmm(&self) -> bool {
        matches!(self.specific_state, PoolSpecificState::RaydiumClmm(_))
    }

    /// 检查是否为Meteora DLMM池
    pub fn is_meteora_dlmm(&self) -> bool {
        matches!(self.specific_state, PoolSpecificState::MeteoraLbClmm(_))
    }

    /// 获取Raydium CLMM池状态引用
    pub fn get_raydium_clmm_state(&self) -> Option<&RaydiumClmmPoolState> {
        match &self.specific_state {
            PoolSpecificState::RaydiumClmm(state) => Some(state),
            _ => None,
        }
    }

    /// 获取Meteora DLMM池状态引用
    pub fn get_meteora_dlmm_state(&self) -> Option<&MeteoraLbPairState> {
        match &self.specific_state {
            PoolSpecificState::MeteoraLbClmm(state) => Some(state),
            _ => None,
        }
    }

    /// 更新Raydium CLMM池状态
    pub fn update_raydium_clmm_state(&mut self, new_state: RaydiumClmmPoolState) -> Result<()> {
        match &mut self.specific_state {
            PoolSpecificState::RaydiumClmm(state) => {
                **state = new_state;
                self.last_updated = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis() as u64;
                self.version += 1;
                Ok(())
            }
            _ => Err(EchoesError::InvalidState(
                "Pool is not a Raydium CLMM pool".to_string(),
            )),
        }
    }

    /// 更新Meteora DLMM池状态
    pub fn update_meteora_dlmm_state(&mut self, new_state: MeteoraLbPairState) -> Result<()> {
        match &mut self.specific_state {
            PoolSpecificState::MeteoraLbClmm(state) => {
                **state = new_state;
                self.last_updated = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_millis() as u64;
                self.version += 1;
                Ok(())
            }
            _ => Err(EchoesError::InvalidState(
                "Pool is not a Meteora DLMM pool".to_string(),
            )),
        }
    }

    /// 从Raydium CLMM状态创建PoolState
    pub fn from_raydium_clmm(
        address: Pubkey,
        raydium_state: RaydiumClmmPoolState,
    ) -> Result<Self> {
        // 从Raydium状态中提取基本代币信息
        let token_a = TokenReserve {
            mint: raydium_state.token_mint_0,
            amount: 0, // 实际余额需要从vault获取
            decimals: raydium_state.mint_decimals_0,
            symbol: None,
        };

        let token_b = TokenReserve {
            mint: raydium_state.token_mint_1,
            amount: 0, // 实际余额需要从vault获取
            decimals: raydium_state.mint_decimals_1,
            symbol: None,
        };

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Ok(Self {
            address,
            pool_type: PoolType::RaydiumClmm,
            token_a,
            token_b,
            fee_recipient: None,
            specific_state: PoolSpecificState::RaydiumClmm(Box::new(raydium_state)),
            last_updated: now,
            last_slot: 0,
            version: 0,
            active: true,
            volume_24h_usd: 0.0,
            tvl_usd: 0.0,
            raw_data_ref: None,
        })
    }

    /// 从Meteora DLMM状态创建PoolState
    pub fn from_meteora_dlmm(
        address: Pubkey,
        meteora_state: MeteoraLbPairState,
    ) -> Result<Self> {
        // 从Meteora状态中提取基本代币信息
        let token_a = TokenReserve {
            mint: meteora_state.token_x_mint,
            amount: 0, // 实际余额需要从各个bin聚合计算
            decimals: meteora_state.token_x_decimals,
            symbol: None,
        };

        let token_b = TokenReserve {
            mint: meteora_state.token_y_mint,
            amount: 0, // 实际余额需要从各个bin聚合计算
            decimals: meteora_state.token_y_decimals,
            symbol: None,
        };

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Ok(Self {
            address,
            pool_type: PoolType::MeteoraLbClmm,
            token_a,
            token_b,
            fee_recipient: None,
            specific_state: PoolSpecificState::MeteoraLbClmm(Box::new(meteora_state)),
            last_updated: now,
            last_slot: 0,
            version: 0,
            active: true,
            volume_24h_usd: 0.0,
            tvl_usd: 0.0,
            raw_data_ref: None,
        })
    }
}

/// 池状态缓存
#[derive(Debug)]
pub struct PoolStateCache {
    /// 池状态映射 pool_address -> PoolState
    pools: Arc<RwLock<HashMap<Pubkey, PoolState>>>,
    /// 按代币对索引 (token_a, token_b) -> Vec<Pubkey>
    token_pair_index: Arc<RwLock<HashMap<(Pubkey, Pubkey), Vec<Pubkey>>>>,
    /// 按池类型索引 PoolType -> Vec<Pubkey>
    pool_type_index: Arc<RwLock<HashMap<PoolType, Vec<Pubkey>>>>,
}

impl Default for PoolStateCache {
    fn default() -> Self {
        Self::new()
    }
}

impl PoolStateCache {
    /// 创建新的池状态缓存
    pub fn new() -> Self {
        Self {
            pools: Arc::new(RwLock::new(HashMap::new())),
            token_pair_index: Arc::new(RwLock::new(HashMap::new())),
            pool_type_index: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 获取池状态（只读）
    pub fn get_pool(&self, address: &Pubkey) -> Option<PoolState> {
        let pools = self.pools.read().unwrap();
        pools.get(address).cloned()
    }

    /// 更新或插入池状态
    pub fn upsert_pool(&self, pool_state: PoolState) -> Result<()> {
        let address = pool_state.address;
        let pool_type = pool_state.pool_type;
        let token_a = pool_state.token_a.mint;
        let token_b = pool_state.token_b.mint;

        // 更新主池映射
        {
            let mut pools = self.pools.write().unwrap();
            pools.insert(address, pool_state);
        }

        // 更新代币对索引
        {
            let mut token_pair_index = self.token_pair_index.write().unwrap();
            let pair = if token_a < token_b {
                (token_a, token_b)
            } else {
                (token_b, token_a)
            };

            token_pair_index
                .entry(pair)
                .or_insert_with(Vec::new)
                .push(address);

            // 去重
            let pools_for_pair = token_pair_index.get_mut(&pair).unwrap();
            pools_for_pair.sort();
            pools_for_pair.dedup();
        }

        // 更新池类型索引
        {
            let mut pool_type_index = self.pool_type_index.write().unwrap();
            pool_type_index
                .entry(pool_type)
                .or_insert_with(Vec::new)
                .push(address);

            // 去重
            let pools_for_type = pool_type_index.get_mut(&pool_type).unwrap();
            pools_for_type.sort();
            pools_for_type.dedup();
        }

        Ok(())
    }

    /// 根据代币对查找池
    pub fn find_pools_by_token_pair(&self, token_a: &Pubkey, token_b: &Pubkey) -> Vec<PoolState> {
        let pair = if token_a < token_b {
            (*token_a, *token_b)
        } else {
            (*token_b, *token_a)
        };

        let pool_addresses = {
            let token_pair_index = self.token_pair_index.read().unwrap();
            token_pair_index.get(&pair).cloned().unwrap_or_default()
        };

        let pools = self.pools.read().unwrap();
        pool_addresses
            .iter()
            .filter_map(|addr| pools.get(addr).cloned())
            .collect()
    }

    /// 根据池类型查找池
    pub fn find_pools_by_type(&self, pool_type: PoolType) -> Vec<PoolState> {
        let pool_addresses = {
            let pool_type_index = self.pool_type_index.read().unwrap();
            pool_type_index.get(&pool_type).cloned().unwrap_or_default()
        };

        let pools = self.pools.read().unwrap();
        pool_addresses
            .iter()
            .filter_map(|addr| pools.get(addr).cloned())
            .collect()
    }

    /// 移除池
    pub fn remove_pool(&self, address: &Pubkey) -> Option<PoolState> {
        let removed_pool = {
            let mut pools = self.pools.write().unwrap();
            pools.remove(address)
        };

        if let Some(ref pool) = removed_pool {
            // 从索引中移除
            let token_a = pool.token_a.mint;
            let token_b = pool.token_b.mint;
            let pool_type = pool.pool_type;

            // 从代币对索引移除
            {
                let mut token_pair_index = self.token_pair_index.write().unwrap();
                let pair = if token_a < token_b {
                    (token_a, token_b)
                } else {
                    (token_b, token_a)
                };

                if let Some(pools_for_pair) = token_pair_index.get_mut(&pair) {
                    pools_for_pair.retain(|&addr| addr != *address);
                    if pools_for_pair.is_empty() {
                        token_pair_index.remove(&pair);
                    }
                }
            }

            // 从池类型索引移除
            {
                let mut pool_type_index = self.pool_type_index.write().unwrap();
                if let Some(pools_for_type) = pool_type_index.get_mut(&pool_type) {
                    pools_for_type.retain(|&addr| addr != *address);
                    if pools_for_type.is_empty() {
                        pool_type_index.remove(&pool_type);
                    }
                }
            }
        }

        removed_pool
    }

    /// 获取缓存统计信息
    pub fn get_stats(&self) -> CacheStats {
        let pools = self.pools.read().unwrap();
        let token_pair_index = self.token_pair_index.read().unwrap();
        let pool_type_index = self.pool_type_index.read().unwrap();

        CacheStats {
            total_pools: pools.len(),
            unique_token_pairs: token_pair_index.len(),
            pool_types_count: pool_type_index.len(),
        }
    }

    /// 清除过期的池状态
    pub fn cleanup_stale_pools(&self, max_age_ms: u64) -> usize {
        let stale_addresses: Vec<Pubkey> = {
            let pools = self.pools.read().unwrap();
            pools
                .iter()
                .filter(|(_, pool)| pool.is_stale(max_age_ms))
                .map(|(addr, _)| *addr)
                .collect()
        };

        let count = stale_addresses.len();
        for address in stale_addresses {
            self.remove_pool(&address);
        }
        count
    }

    /// 清空所有缓存
    pub fn clear(&self) {
        let mut pools = self.pools.write().unwrap();
        let mut token_pair_index = self.token_pair_index.write().unwrap();
        let mut pool_type_index = self.pool_type_index.write().unwrap();

        pools.clear();
        token_pair_index.clear();
        pool_type_index.clear();
    }
}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_pools: usize,
    pub unique_token_pairs: usize,
    pub pool_types_count: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use solana_sdk::pubkey::Pubkey;

    fn create_test_pool() -> PoolState {
        let token_a = TokenReserve {
            mint: Pubkey::new_unique(),
            amount: 1000000,
            decimals: 6,
            symbol: Some("USDC".to_string()),
        };

        let token_b = TokenReserve {
            mint: Pubkey::new_unique(),
            amount: 1000000000000,
            decimals: 9,
            symbol: Some("SOL".to_string()),
        };

        let cpmm_state = CpmmPoolState {
            k_value: 1000000000000000000,
            fee_rate: 30, // 0.3%
            lp_supply: 1000000,
        };

        PoolState::new(
            Pubkey::new_unique(),
            PoolType::RaydiumCpmm,
            token_a,
            token_b,
            PoolSpecificState::Cpmm(cpmm_state),
        )
    }

    #[test]
    fn test_pool_state_creation() {
        let pool = create_test_pool();
        assert_eq!(pool.pool_type, PoolType::RaydiumCpmm);
        assert_eq!(pool.version, 0);
        assert!(pool.active);
    }

    #[test]
    fn test_pool_state_update() {
        let mut pool = create_test_pool();
        let initial_version = pool.version;

        pool.update_reserves(2000000, 2000000000000, 12345);

        assert_eq!(pool.token_a.amount, 2000000);
        assert_eq!(pool.token_b.amount, 2000000000000);
        assert_eq!(pool.last_slot, 12345);
        assert_eq!(pool.version, initial_version + 1);
    }

    #[test]
    fn test_cache_operations() {
        let cache = PoolStateCache::new();
        let pool = create_test_pool();
        let address = pool.address;

        // 插入池
        cache.upsert_pool(pool.clone()).unwrap();

        // 获取池
        let retrieved = cache.get_pool(&address).unwrap();
        assert_eq!(retrieved.address, address);

        // 通过代币对查找
        let pools = cache.find_pools_by_token_pair(&pool.token_a.mint, &pool.token_b.mint);
        assert_eq!(pools.len(), 1);

        // 通过类型查找
        let pools_by_type = cache.find_pools_by_type(PoolType::RaydiumCpmm);
        assert_eq!(pools_by_type.len(), 1);

        // 统计信息
        let stats = cache.get_stats();
        assert_eq!(stats.total_pools, 1);
        assert_eq!(stats.unique_token_pairs, 1);
    }

    #[test]
    fn test_price_calculation() {
        let pool = create_test_pool();
        let price = pool.calculate_price_a_to_b().unwrap();

        // 基于储备量计算的价格
        // token_b.amount / token_a.amount * 10^(decimals_diff)
        // 1000000000000 / 1000000 * 10^(9-6) = 1000000 * 1000 = 1000000000
        assert!((price - 1000000000.0).abs() < 0.001);
    }

    #[test]
    fn test_swap_estimation() {
        let pool = create_test_pool();

        // 输入100,000 tokenA，估算能得到多少tokenB
        let input_amount = 100000;
        let output = pool.estimate_swap_output(input_amount, true).unwrap();

        // 应该根据恒定乘积公式计算
        assert!(output > 0);
        assert!(output < pool.token_b.amount); // 输出应该少于总储备
    }
}
