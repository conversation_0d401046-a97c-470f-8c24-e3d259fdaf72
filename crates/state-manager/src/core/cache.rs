//! 核心缓存系统
//!
//! 提供高效的内存缓存功能，支持池状态的快速访问和更新。

use super::types::*;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{SystemTime, UNIX_EPOCH};

/// 缓存项
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct CacheItem<T> {
    /// 数据
    pub data: T,
    /// 创建时间
    pub created_at: u64,
    /// 最后访问时间
    pub last_accessed: u64,
    /// 访问次数
    pub access_count: u64,
    /// 版本号
    pub version: u64,
}

impl<T> CacheItem<T> {
    /// 创建新的缓存项
    pub fn new(data: T) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Self {
            data,
            created_at: now,
            last_accessed: now,
            access_count: 1,
            version: 1,
        }
    }

    /// 更新访问信息
    pub fn touch(&mut self) {
        self.last_accessed = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        self.access_count += 1;
    }

    /// 获取数据引用
    pub fn get(&mut self) -> &T {
        self.touch();
        &self.data
    }

    /// 获取数据的可变引用
    pub fn get_mut(&mut self) -> &mut T {
        self.touch();
        &mut self.data
    }

    /// 更新数据
    pub fn update(&mut self, data: T) {
        self.data = data;
        self.version += 1;
        self.touch();
    }

    /// 检查是否过期
    pub fn is_expired(&self, ttl_ms: u64) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        now - self.created_at > ttl_ms
    }
}

/// 通用缓存接口
pub trait Cache<K, V>: Send + Sync {
    /// 获取值
    fn get(&self, key: &K) -> Option<V>;

    /// 设置值
    fn set(&self, key: K, value: V) -> PoolResult<()>;

    /// 移除值
    fn remove(&self, key: &K) -> Option<V>;

    /// 清空缓存
    fn clear(&self);

    /// 获取缓存大小
    fn size(&self) -> usize;

    /// 获取所有键
    fn keys(&self) -> Vec<K>;
}

/// 简单的内存缓存实现
#[derive(Debug)]
pub struct MemoryCache<K, V>
where
    K: Clone + Eq + std::hash::Hash,
    V: Clone,
{
    /// 缓存数据
    cache: Arc<RwLock<HashMap<K, CacheItem<V>>>>,
    /// 最大缓存大小
    max_size: usize,
    /// TTL (毫秒)
    ttl_ms: u64,
}

impl<K, V> MemoryCache<K, V>
where
    K: Clone + Eq + std::hash::Hash,
    V: Clone,
{
    /// 创建新的缓存
    pub fn new(max_size: usize, ttl_ms: u64) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            max_size,
            ttl_ms,
        }
    }

    /// 清理过期项
    pub fn cleanup_expired(&self) {
        let mut cache = self.cache.write().unwrap();
        cache.retain(|_, item| !item.is_expired(self.ttl_ms));
    }

    /// 移除最少使用的项
    fn evict_lru(&self, cache: &mut HashMap<K, CacheItem<V>>) {
        if cache.is_empty() {
            return;
        }

        let mut lru_key = None;
        let mut lru_time = u64::MAX;

        for (key, item) in cache.iter() {
            if item.last_accessed < lru_time {
                lru_time = item.last_accessed;
                lru_key = Some(key.clone());
            }
        }

        if let Some(key) = lru_key {
            cache.remove(&key);
        }
    }

    /// 获取缓存统计
    pub fn stats(&self) -> PoolCacheStats {
        let cache = self.cache.read().unwrap();
        let total_access_count: u64 = cache.values().map(|item| item.access_count).sum();

        PoolCacheStats {
            size: cache.len(),
            max_size: self.max_size,
            hit_count: total_access_count,
            ttl_ms: self.ttl_ms,
        }
    }
}

impl<K, V> Cache<K, V> for MemoryCache<K, V>
where
    K: Clone + Eq + std::hash::Hash + std::marker::Send + std::marker::Sync,
    V: Clone + std::marker::Send + std::marker::Sync,
{
    fn get(&self, key: &K) -> Option<V> {
        let mut cache = self.cache.write().unwrap();

        if let Some(item) = cache.get_mut(key) {
            if item.is_expired(self.ttl_ms) {
                cache.remove(key);
                return None;
            }

            Some(item.get().clone())
        } else {
            None
        }
    }

    fn set(&self, key: K, value: V) -> PoolResult<()> {
        let mut cache = self.cache.write().unwrap();

        // 如果缓存已满，移除最少使用的项
        if cache.len() >= self.max_size {
            self.evict_lru(&mut cache);
        }

        cache.insert(key, CacheItem::new(value));
        Ok(())
    }

    fn remove(&self, key: &K) -> Option<V> {
        let mut cache = self.cache.write().unwrap();
        cache.remove(key).map(|item| item.data)
    }

    fn clear(&self) {
        let mut cache = self.cache.write().unwrap();
        cache.clear();
    }

    fn size(&self) -> usize {
        let cache = self.cache.read().unwrap();
        cache.len()
    }

    fn keys(&self) -> Vec<K> {
        let cache = self.cache.read().unwrap();
        cache.keys().cloned().collect()
    }
}

impl<K, V> Default for MemoryCache<K, V>
where
    K: Clone + Eq + std::hash::Hash,
    V: Clone,
{
    fn default() -> Self {
        Self::new(1000, 300_000) // 默认1000个项目，5分钟TTL
    }
}

/// 池缓存统计信息
#[derive(Debug, Clone)]
pub struct PoolCacheStats {
    /// 当前大小
    pub size: usize,
    /// 最大大小
    pub max_size: usize,
    /// 命中次数
    pub hit_count: u64,
    /// TTL (毫秒)
    pub ttl_ms: u64,
}
