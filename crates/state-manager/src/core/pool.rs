//! 通用池接口和基础结构
//!
//! 定义所有类型池的通用接口和基础功能

use super::types::*;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::time::{SystemTime, UNIX_EPOCH};

/// 池状态 trait
/// 所有类型的池都必须实现这个 trait
pub trait PoolState: Send + Sync + Clone {
    /// 获取池标识符
    fn pool_id(&self) -> &PoolId;

    /// 获取代币A储备
    fn token_a_reserve(&self) -> &TokenReserve;

    /// 获取代币B储备
    fn token_b_reserve(&self) -> &TokenReserve;

    /// 计算A->B的价格
    fn calculate_price_a_to_b(&self) -> PoolResult<f64>;

    /// 计算B->A的价格
    fn calculate_price_b_to_a(&self) -> PoolResult<f64> {
        let price_a_to_b = self.calculate_price_a_to_b()?;
        if price_a_to_b == 0.0 {
            return Err(PoolError::CalculationError("Price is zero".to_string()));
        }
        Ok(1.0 / price_a_to_b)
    }

    /// 估算交换输出
    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
    ) -> PoolResult<SwapQuote>;

    /// 获取流动性快照
    fn get_liquidity_snapshot(&self) -> PoolResult<LiquiditySnapshot>;

    /// 获取价格信息
    fn get_price_info(&self) -> PoolResult<PriceInfo>;

    /// 检查池是否活跃
    fn is_active(&self) -> bool;

    /// 获取最后更新时间
    fn last_updated(&self) -> u64;

    /// 获取费率（基点）
    fn fee_rate(&self) -> u16;
}

/// 通用池状态实现
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenericPoolState {
    /// 池标识符
    pub pool_id: PoolId,
    /// 代币A储备
    pub token_a: TokenReserve,
    /// 代币B储备
    pub token_b: TokenReserve,
    /// 费率（基点）
    pub fee_rate: u16,
    /// 是否活跃
    pub active: bool,
    /// 最后更新时间
    pub last_updated: u64,
    /// 24小时交易量（USD）
    pub volume_24h_usd: f64,
    /// 总价值锁定（USD）
    pub tvl_usd: f64,
    /// 版本号
    pub version: u64,
}

impl GenericPoolState {
    /// 创建新的通用池状态
    pub fn new(
        protocol: DexProtocol,
        pool_type: PoolType,
        address: Pubkey,
        token_a: TokenReserve,
        token_b: TokenReserve,
        fee_rate: u16,
    ) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        Self {
            pool_id: PoolId::new(protocol, pool_type, address),
            token_a,
            token_b,
            fee_rate,
            active: true,
            last_updated: now,
            volume_24h_usd: 0.0,
            tvl_usd: 0.0,
            version: 0,
        }
    }

    /// 更新储备
    pub fn update_reserves(&mut self, token_a_amount: u64, token_b_amount: u64) {
        self.token_a.amount = token_a_amount;
        self.token_b.amount = token_b_amount;
        self.last_updated = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        self.version += 1;
    }

    /// 设置活跃状态
    pub fn set_active(&mut self, active: bool) {
        self.active = active;
        self.last_updated = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        self.version += 1;
    }

    /// 更新交易量
    pub fn update_volume(&mut self, volume_24h_usd: f64) {
        self.volume_24h_usd = volume_24h_usd;
        self.last_updated = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        self.version += 1;
    }

    /// 更新TVL
    pub fn update_tvl(&mut self, tvl_usd: f64) {
        self.tvl_usd = tvl_usd;
        self.last_updated = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;
        self.version += 1;
    }
}

impl PoolState for GenericPoolState {
    fn pool_id(&self) -> &PoolId {
        &self.pool_id
    }

    fn token_a_reserve(&self) -> &TokenReserve {
        &self.token_a
    }

    fn token_b_reserve(&self) -> &TokenReserve {
        &self.token_b
    }

    fn calculate_price_a_to_b(&self) -> PoolResult<f64> {
        if self.token_a.amount == 0 {
            return Err(PoolError::CalculationError("Token A amount is zero".to_string()));
        }

        // 简单的恒定乘积价格计算
        let price = (self.token_b.amount as f64 / 10_f64.powi(self.token_b.decimals as i32))
            / (self.token_a.amount as f64 / 10_f64.powi(self.token_a.decimals as i32));

        Ok(price)
    }

    fn estimate_swap_output(
        &self,
        input_amount: u64,
        direction: SwapDirection,
    ) -> PoolResult<SwapQuote> {
        if input_amount == 0 {
            return Err(PoolError::CalculationError("Input amount cannot be zero".to_string()));
        }

        let (input_token, output_token, input_reserve, output_reserve) = match direction {
            SwapDirection::AToB => (
                self.token_a.mint,
                self.token_b.mint,
                self.token_a.amount,
                self.token_b.amount,
            ),
            SwapDirection::BToA => (
                self.token_b.mint,
                self.token_a.mint,
                self.token_b.amount,
                self.token_a.amount,
            ),
        };

        if output_reserve == 0 {
            return Err(PoolError::CalculationError("Output reserve is zero".to_string()));
        }

        // 恒定乘积公式: (x + Δx) * (y - Δy) = x * y
        // Δy = y * Δx / (x + Δx)
        let fee_multiplier = (10000 - self.fee_rate) as f64 / 10000.0;
        let input_after_fee = (input_amount as f64 * fee_multiplier) as u64;
        
        let k = input_reserve as u128 * output_reserve as u128;
        let new_input_reserve = input_reserve as u128 + input_after_fee as u128;
        let new_output_reserve = k / new_input_reserve;
        let output_amount = output_reserve as u128 - new_output_reserve;

        let fee_amount = input_amount - input_after_fee;
        
        // 计算价格影响
        let price_before = output_reserve as f64 / input_reserve as f64;
        let price_after = new_output_reserve as f64 / new_input_reserve as f64;
        let price_impact = ((price_before - price_after) / price_before * 100.0).abs();

        Ok(SwapQuote {
            input_token,
            output_token,
            input_amount,
            output_amount: output_amount as u64,
            fee_amount,
            price_impact,
            direction,
            minimum_output: None,
            route: vec![self.pool_id.clone()],
        })
    }

    fn get_liquidity_snapshot(&self) -> PoolResult<LiquiditySnapshot> {
        Ok(LiquiditySnapshot {
            total_value_usd: self.tvl_usd,
            token_a_reserve: self.token_a.clone(),
            token_b_reserve: self.token_b.clone(),
            volume_24h_usd: self.volume_24h_usd,
            apy: None,
            fee_rate: self.fee_rate,
        })
    }

    fn get_price_info(&self) -> PoolResult<PriceInfo> {
        let price = self.calculate_price_a_to_b()?;
        let inverse_price = if price != 0.0 { 1.0 / price } else { 0.0 };

        Ok(PriceInfo {
            price,
            inverse_price,
            price_change_24h: None,
            last_updated: self.last_updated,
        })
    }

    fn is_active(&self) -> bool {
        self.active
    }

    fn last_updated(&self) -> u64 {
        self.last_updated
    }

    fn fee_rate(&self) -> u16 {
        self.fee_rate
    }
}
