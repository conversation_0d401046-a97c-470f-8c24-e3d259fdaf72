//! 统一池管理器
//!
//! 提供跨 DEX 的统一池管理接口

use super::types::*;
use super::manager::*;
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;

/// 统一池管理器
/// 管理所有 DEX 的所有池类型
#[derive(Debug)]
pub struct UnifiedPoolManager {
    /// 池管理器注册表
    registry: PoolManagerRegistry,
    /// 配置
    config: UnifiedManagerConfig,
}

/// 统一管理器配置
#[derive(Debug, Clone)]
pub struct UnifiedManagerConfig {
    /// 缓存大小限制
    pub max_cache_size: usize,
    /// 缓存 TTL (毫秒)
    pub cache_ttl_ms: u64,
    /// 是否启用自动清理
    pub enable_auto_cleanup: bool,
    /// 清理间隔 (毫秒)
    pub cleanup_interval_ms: u64,
}

impl Default for UnifiedManagerConfig {
    fn default() -> Self {
        Self {
            max_cache_size: 10000,
            cache_ttl_ms: 300_000, // 5分钟
            enable_auto_cleanup: true,
            cleanup_interval_ms: 60_000, // 1分钟
        }
    }
}

impl UnifiedPoolManager {
    /// 创建新的统一管理器
    pub fn new(config: UnifiedManagerConfig) -> Self {
        Self {
            registry: PoolManagerRegistry::new(),
            config,
        }
    }

    /// 使用默认配置创建
    pub fn with_default_config() -> Self {
        Self::new(UnifiedManagerConfig::default())
    }

    /// 注册池
    pub fn register_pool(&mut self, pool_id: PoolId) {
        self.registry.register(pool_id);
    }

    /// 检查池是否存在
    pub fn has_pool(&self, pool_id: &PoolId) -> bool {
        self.registry.contains(pool_id)
    }

    /// 估算交换（简化版本）
    pub fn estimate_swap(
        &self,
        input_token: &Pubkey,
        output_token: &Pubkey,
        _input_amount: u64,
        _slippage_tolerance: Option<f64>,
    ) -> PoolResult<Vec<SwapQuote>> {
        // 简化实现 - 返回空的报价列表
        let _pools = self.find_pools_for_tokens(input_token, output_token);

        // 这里应该实际调用各个池的估算方法
        // 但为了避免复杂的 trait object 问题，暂时返回空列表
        Ok(Vec::new())
    }

    /// 获取最佳交换报价
    pub fn get_best_swap_quote(
        &self,
        input_token: &Pubkey,
        output_token: &Pubkey,
        input_amount: u64,
        slippage_tolerance: Option<f64>,
    ) -> PoolResult<SwapQuote> {
        let quotes = self.estimate_swap(input_token, output_token, input_amount, slippage_tolerance)?;

        quotes.into_iter().next().ok_or_else(|| {
            PoolError::PoolNotFound(PoolId::new(
                DexProtocol::Raydium, // 占位符
                PoolType::Cpmm,       // 占位符
                *input_token,
            ))
        })
    }

    /// 查找支持指定代币对的所有池（简化版本）
    pub fn find_pools_for_tokens(
        &self,
        _token_a: &Pubkey,
        _token_b: &Pubkey,
    ) -> Vec<PoolId> {
        // 简化实现 - 返回所有注册的池ID
        self.registry.get_all_pool_ids()
    }

    /// 按协议获取池
    pub fn get_pools_by_protocol(&self, protocol: DexProtocol) -> Vec<PoolId> {
        self.registry.get_by_protocol(protocol)
    }

    /// 按池类型获取池
    pub fn get_pools_by_type(&self, pool_type: PoolType) -> Vec<PoolId> {
        self.registry.get_by_pool_type(pool_type)
    }

    /// 获取所有池的统计信息
    pub fn get_global_stats(&self) -> GlobalStats {
        let registry_stats = self.registry.get_stats();

        GlobalStats {
            total_pools: registry_stats.total_pools,
            pools_by_protocol: registry_stats.pools_by_protocol,
            pools_by_type: registry_stats.pools_by_type,
            active_pools: registry_stats.active_pools,
            total_liquidity_usd: registry_stats.total_liquidity_usd,
            cache_size: 0, // 简化处理
            last_updated: registry_stats.last_updated,
        }
    }

    /// 获取流动性最高的池（简化版本）
    pub fn get_top_pools_by_liquidity(&self, limit: usize) -> Vec<(PoolId, f64)> {
        // 简化实现 - 返回前N个池，流动性设为0
        self.registry.get_all_pool_ids()
            .into_iter()
            .take(limit)
            .map(|pool_id| (pool_id, 0.0))
            .collect()
    }

    /// 获取交易量最高的池（简化版本）
    pub fn get_top_pools_by_volume(&self, limit: usize) -> Vec<(PoolId, f64)> {
        // 简化实现 - 返回前N个池，交易量设为0
        self.registry.get_all_pool_ids()
            .into_iter()
            .take(limit)
            .map(|pool_id| (pool_id, 0.0))
            .collect()
    }

    /// 移除池
    pub fn remove_pool(&mut self, pool_id: &PoolId) -> bool {
        self.registry.remove(pool_id)
    }

    /// 获取配置
    pub fn get_config(&self) -> &UnifiedManagerConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: UnifiedManagerConfig) {
        self.config = config;
    }
}

/// 全局统计信息
#[derive(Debug, Clone)]
pub struct GlobalStats {
    /// 总池数量
    pub total_pools: usize,
    /// 按协议分组的池数量
    pub pools_by_protocol: HashMap<DexProtocol, usize>,
    /// 按类型分组的池数量
    pub pools_by_type: HashMap<PoolType, usize>,
    /// 活跃池数量
    pub active_pools: usize,
    /// 总流动性价值（USD）
    pub total_liquidity_usd: f64,
    /// 缓存大小
    pub cache_size: usize,
    /// 最后更新时间
    pub last_updated: u64,
}
