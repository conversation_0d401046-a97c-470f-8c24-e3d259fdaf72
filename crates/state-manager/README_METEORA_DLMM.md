# Meteora DLMM 状态管理器

本文档描述了为 Meteora DLMM (Dynamic Liquidity Market Maker) 池实现的本地状态管理和报价计算系统。

## 概述

Meteora DLMM 是一种基于离散流动性分布的 AMM 协议，与传统的连续流动性模型不同，DLMM 将流动性分布在离散的价格区间（bins）中。本实现提供了：

- **完整的链上数据结构映射**：基于真实的 Meteora 链上数据结构
- **高效的本地缓存系统**：支持实时状态更新和查询
- **精确的交换计算**：跨多个 bin 的复杂交换路径计算
- **流动性分析工具**：深入的流动性分布分析

## 核心组件

### 1. 数据结构

#### MeteoraLbBin
表示单个价格区间的流动性状态：
```rust
pub struct MeteoraLbBin {
    pub amount_x: u64,           // X代币数量
    pub amount_y: u64,           // Y代币数量
    pub price: u128,             // 价格 (Q64.64格式)
    pub liquidity_supply: u128,  // 流动性供应量
    // ... 其他字段
}
```

#### MeteoraLbBinArray
包含一组连续的价格区间：
```rust
pub struct MeteoraLbBinArray {
    pub index: i64,              // 数组索引
    pub lb_pair: Pubkey,         // 关联的LB Pair地址
    pub bins: Vec<MeteoraLbBin>, // Bin数组
    // ... 其他字段
}
```

#### MeteoraLbPairState
表示完整的 DLMM 流动性池：
```rust
pub struct MeteoraLbPairState {
    pub address: Pubkey,                    // 池地址
    pub token_x_mint: Pubkey,               // X代币铸币地址
    pub token_y_mint: Pubkey,               // Y代币铸币地址
    pub active_id: i32,                     // 当前活跃的bin ID
    pub bin_step: u16,                      // bin步长
    pub bin_arrays: BTreeMap<i64, MeteoraLbBinArray>, // Bin数组映射
    // ... 其他字段
}
```

### 2. 池管理器

#### MeteoraLbPoolManager
负责管理 DLMM 池的状态和计算交换：

```rust
impl MeteoraLbPoolManager {
    // 创建池管理器
    pub fn new(pool_state: MeteoraLbPairState) -> Self;
    
    // 从JSON数据创建
    pub fn from_json_data(
        address: Pubkey,
        lb_pair_data: &str,
        bin_arrays_data: &[&str],
        bitmap_data: Option<&str>,
    ) -> Result<Self>;
    
    // 估算交换输出
    pub fn estimate_swap_output(
        &self,
        input_amount: u64,
        input_is_x: bool,
        max_price_impact: Option<f64>,
    ) -> Result<SwapEstimation>;
    
    // 获取当前价格
    pub fn get_current_price(&self) -> Result<f64>;
    
    // 获取流动性信息
    pub fn get_liquidity_info(&self) -> LiquidityInfo;
}
```

### 3. 缓存系统

#### MeteoraLbPoolCache
高效的内存缓存系统：

```rust
impl MeteoraLbPoolCache {
    // 添加或更新池
    pub fn upsert_pool(&self, address: Pubkey, manager: MeteoraLbPoolManager) -> Result<()>;
    
    // 获取池管理器
    pub fn get_pool(&self, address: &Pubkey) -> Option<MeteoraLbPoolManager>;
    
    // 根据代币对查找池
    pub fn find_pools_by_tokens(&self, token_x: &Pubkey, token_y: &Pubkey) -> Vec<(Pubkey, MeteoraLbPoolManager)>;
    
    // 获取缓存统计
    pub fn get_stats(&self) -> CacheStats;
}
```

## 核心功能

### 1. 价格计算

DLMM 使用基于 bin 步长的价格计算：
```rust
// 价格公式: price = (1 + bin_step / 10000)^bin_id
pub fn get_bin_price(&self, bin_id: i32) -> Result<u128> {
    let bin_step_decimal = self.bin_step as f64 / 10000.0;
    let base = 1.0 + bin_step_decimal;
    let price_float = base.powf(bin_id as f64);
    
    // 转换为Q64.64格式
    let price_q64 = (price_float * (1u128 << 64) as f64) as u128;
    Ok(price_q64)
}
```

### 2. 交换估算

支持跨多个 bin 的复杂交换计算：

```rust
pub struct SwapEstimation {
    pub input_amount: u64,           // 输入金额
    pub output_amount: u64,          // 输出金额
    pub fee_amount: u64,             // 费用金额
    pub price_impact: f64,           // 价格影响（百分比）
    pub bins_used: Vec<BinUsage>,    // 使用的bin信息
    pub remaining_input: u64,        // 剩余未处理的输入
}
```

### 3. 流动性分析

提供详细的流动性分布分析：

```rust
pub struct LiquidityInfo {
    pub total_liquidity: u128,                      // 总流动性
    pub active_bin_count: usize,                    // 活跃bin数量
    pub price_range: (u128, u128),                  // 价格范围
    pub distribution: Vec<(i32, u128, u128)>,       // 流动性分布
}
```

## 使用示例

### 基本使用

```rust
use state_manager::meteora_dlmm::*;

// 1. 创建池状态
let mut pool_state = MeteoraLbPairState::new(
    pool_address,
    token_x_mint,
    token_y_mint,
    6, // USDC decimals
    9, // SOL decimals
    8388608, // active_id
    25, // bin_step (0.25%)
);

// 2. 创建池管理器
let manager = MeteoraLbPoolManager::new(pool_state);

// 3. 估算交换
let estimation = manager.estimate_swap_output(
    1000_000_000, // 1000 USDC
    true,         // USDC -> SOL
    Some(5.0),    // 最大5%价格影响
)?;

println!("输出: {} SOL", estimation.output_amount as f64 / 1_000_000_000.0);
println!("费用: {} USDC", estimation.fee_amount as f64 / 1_000_000.0);
println!("价格影响: {:.2}%", estimation.price_impact);
```

### 从JSON数据创建

```rust
// 从链上JSON数据创建池管理器
let manager = MeteoraLbPoolManager::from_json_data(
    pool_address,
    &lb_pair_json_data,
    &[&bin_array_json_1, &bin_array_json_2],
    Some(&bitmap_json_data),
)?;
```

### 缓存管理

```rust
// 创建缓存
let cache = MeteoraLbPoolCache::new();

// 添加池
cache.upsert_pool(pool_address, manager)?;

// 查询池
if let Some(cached_manager) = cache.get_pool(&pool_address) {
    let price = cached_manager.get_current_price()?;
    println!("当前价格: {:.6}", price);
}

// 根据代币对查找
let pools = cache.find_pools_by_tokens(&usdc_mint, &sol_mint);
```

## 数学常量

```rust
pub mod math_constants {
    pub const BASIS_POINT_MAX: u64 = 10000;        // 基础点精度
    pub const MAX_BIN_PER_ARRAY: usize = 70;       // 最大bin数量
    pub const PRICE_PRECISION_BITS: u32 = 64;      // 价格精度位数
    pub const MAX_BIN_ID: i32 = 443636;            // 最大bin ID
    pub const MIN_BIN_ID: i32 = -443636;           // 最小bin ID
}
```

## 工具函数

```rust
pub mod utils {
    // Q64.64格式转换
    pub fn q64_to_float(q64_price: u128) -> f64;
    pub fn float_to_q64(price: f64) -> u128;
    
    // 价格差异计算
    pub fn calculate_price_difference(price1: u128, price2: u128) -> f64;
    
    // 流动性格式化
    pub fn format_liquidity(liquidity: u128) -> String;
    
    // 从JSON文件加载池数据
    pub async fn load_pool_from_json_files(
        address: Pubkey,
        lb_pair_file: &str,
        bin_array_files: &[&str],
        bitmap_file: Option<&str>,
    ) -> Result<MeteoraLbPoolManager>;
}
```

## 测试

运行测试：
```bash
cd crates/state-manager
cargo test meteora_dlmm --lib
```

运行示例：
```bash
cargo run --example meteora_dlmm_example
```

## 特性

- ✅ **完整的数据结构支持**：映射所有 Meteora DLMM 链上数据结构
- ✅ **精确的价格计算**：基于真实的 bin 步长和价格公式
- ✅ **高效的交换估算**：支持跨多个 bin 的复杂交换路径
- ✅ **位图优化查找**：使用位图快速定位活跃的 bin 数组
- ✅ **内存缓存系统**：高性能的本地状态缓存
- ✅ **流动性分析**：详细的流动性分布和统计信息
- ✅ **JSON数据支持**：直接从链上JSON数据创建池状态
- ✅ **完整的测试覆盖**：包含单元测试和集成示例

## 性能优化

1. **位图索引**：使用位图快速定位活跃的 bin 数组
2. **内存缓存**：避免重复的链上数据查询
3. **惰性计算**：只在需要时计算复杂的数学运算
4. **批量操作**：支持批量更新和查询操作

## 未来扩展

- [ ] 支持更多的 Meteora 池类型
- [ ] 实时数据流更新
- [ ] 历史数据分析
- [ ] 高级套利策略
- [ ] WebSocket 实时价格推送
