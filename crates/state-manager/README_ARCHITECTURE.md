# State Manager 架构重构

本文档描述了 State Manager 的新架构设计，支持多个 DEX 和不同类型的池子。

## 🏗️ 架构概览

新的架构采用分层设计，提供了清晰的抽象层次和模块化的组织结构：

```
crates/state-manager/src/
├── core/                           # 核心抽象层
│   ├── types.rs                    # 通用类型定义
│   ├── pool.rs                     # 池状态抽象
│   ├── manager.rs                  # 池管理器抽象
│   ├── cache.rs                    # 缓存系统
│   └── unified_manager.rs          # 统一管理器
├── dex/                           # DEX 特定实现
│   ├── raydium/                   # Raydium DEX
│   │   └── clmm/                  # CLMM 池实现
│   │       ├── types.rs           # Raydium CLMM 类型
│   │       ├── pool.rs            # 池状态实现
│   │       └── manager.rs         # 管理器实现
│   └── meteora/                   # Meteora DEX
│       └── dlmm/                  # DLMM 池实现
│           ├── types.rs           # Meteora DLMM 类型
│           ├── pool.rs            # 池状态实现
│           └── manager.rs         # 管理器实现
├── utils/                         # 工具函数
│   ├── math.rs                    # 数学计算
│   ├── json.rs                    # JSON 处理
│   └── format.rs                  # 格式化工具
└── data/                          # 测试数据
    ├── raydium/clmm/             # Raydium CLMM 数据
    └── meteora/dlmm/             # Meteora DLMM 数据
```

## 🎯 设计原则

### 1. 分层抽象
- **核心层**: 定义通用接口和抽象
- **DEX层**: 实现特定协议的逻辑
- **工具层**: 提供通用的辅助功能

### 2. 协议无关
- 统一的接口设计，支持任意 DEX 协议
- 可插拔的架构，易于添加新的 DEX

### 3. 类型安全
- 强类型系统，编译时错误检查
- 明确的错误处理和结果类型

### 4. 性能优化
- 高效的缓存系统
- 惰性计算和批量操作
- 内存友好的设计

## 🔧 核心组件

### 1. 通用类型系统

```rust
/// DEX 协议类型
pub enum DexProtocol {
    Raydium,
    Meteora,
    Orca,
    // ...
}

/// 池类型
pub enum PoolType {
    Cpmm,    // 恒定乘积
    Clmm,    // 集中流动性
    Dlmm,    // 动态流动性
    Stable,  // 稳定币池
    // ...
}

/// 池标识符
pub struct PoolId {
    pub protocol: DexProtocol,
    pub pool_type: PoolType,
    pub address: Pubkey,
}
```

### 2. 池状态抽象

```rust
/// 池状态 trait
pub trait PoolState: Send + Sync + Clone {
    fn pool_id(&self) -> &PoolId;
    fn token_a_reserve(&self) -> &TokenReserve;
    fn token_b_reserve(&self) -> &TokenReserve;
    fn calculate_price_a_to_b(&self) -> PoolResult<f64>;
    fn estimate_swap_output(&self, input_amount: u64, direction: SwapDirection) -> PoolResult<SwapQuote>;
    // ...
}
```

### 3. 池管理器抽象

```rust
/// 池管理器 trait
pub trait PoolManager: Send + Sync {
    type State: PoolState;
    
    fn get_state(&self) -> &Self::State;
    fn estimate_swap(&self, input_amount: u64, direction: SwapDirection, slippage_tolerance: Option<f64>) -> PoolResult<SwapQuote>;
    fn get_current_price(&self, direction: SwapDirection) -> PoolResult<f64>;
    // ...
}
```

### 4. 统一管理器

```rust
/// 统一池管理器
pub struct UnifiedPoolManager {
    registry: PoolManagerRegistry,
    cache: Arc<RwLock<HashMap<PoolId, Box<dyn PoolState>>>>,
    config: UnifiedManagerConfig,
}

impl UnifiedPoolManager {
    // 跨 DEX 交换估算
    pub fn estimate_swap(&self, input_token: &Pubkey, output_token: &Pubkey, input_amount: u64, slippage_tolerance: Option<f64>) -> PoolResult<Vec<SwapQuote>>;
    
    // 获取最佳报价
    pub fn get_best_swap_quote(&self, input_token: &Pubkey, output_token: &Pubkey, input_amount: u64, slippage_tolerance: Option<f64>) -> PoolResult<SwapQuote>;
    
    // 查找支持代币对的池
    pub fn find_pools_for_tokens(&self, token_a: &Pubkey, token_b: &Pubkey) -> Vec<(PoolId, Arc<dyn PoolManager<State = Box<dyn PoolState>>>)>;
}
```

## 🚀 使用示例

### 基本使用

```rust
use state_manager::core::types::*;
use state_manager::core::unified_manager::*;

// 创建统一管理器
let mut manager = UnifiedPoolManager::with_default_config();

// 注册池管理器
manager.register_pool_manager(pool_id, pool_manager);

// 估算交换
let quotes = manager.estimate_swap(&usdc_mint, &sol_mint, 1000_000_000, Some(1.0))?;

// 获取最佳报价
let best_quote = manager.get_best_swap_quote(&usdc_mint, &sol_mint, 1000_000_000, Some(1.0))?;
```

### DEX 特定使用

```rust
use state_manager::dex::raydium::clmm::*;
use state_manager::dex::meteora::dlmm::*;

// Raydium CLMM
let raydium_manager = RaydiumClmmPoolManager::new(raydium_state);
let raydium_quote = raydium_manager.estimate_swap(input_amount, SwapDirection::AToB, Some(1.0))?;

// Meteora DLMM
let meteora_manager = MeteoraLbPoolManager::new(meteora_state);
let meteora_quote = meteora_manager.estimate_swap_output(input_amount, true, Some(5.0))?;
```

## 📊 工具函数

### 数学计算

```rust
use state_manager::utils::math::*;

// Q64.64 格式转换
let price_float = q64::to_float(q64_price);
let q64_price = q64::from_float(1.5);

// 价格计算
let price_impact = price::calculate_price_impact(price_before, price_after);
let weighted_price = price::calculate_weighted_average_price(&prices);

// 交换计算
let output = swap::calculate_cpmm_output(input_amount, input_reserve, output_reserve, fee_rate)?;
```

### 格式化工具

```rust
use state_manager::utils::format::*;

// 数字格式化
let formatted = NumberFormatter::format_large_number(1_500_000); // "1.50M"
let usd = NumberFormatter::format_usd(123.45); // "$123.45"
let percentage = NumberFormatter::format_percentage(12.34, Some(2)); // "12.34%"

// 地址格式化
let short_addr = AddressFormatter::shorten_pubkey(&pubkey, Some(6)); // "1111...1111"
let pool_id_str = AddressFormatter::format_pool_id(&pool_id, true);
```

### JSON 处理

```rust
use state_manager::utils::json::*;

// 安全解析
let pubkey = JsonParser::extract_pubkey(&json, "address")?;
let amount = JsonParser::extract_u64(&json, "amount")?;
let nested_value = JsonParser::extract_nested(&json, &["parsed", "data"])?;

// 构建 JSON
let pool_json = JsonBuilder::build_pool_state_json(&pool_id, &token_a, &token_b, None);
let quote_json = JsonBuilder::build_swap_quote_json(&quote);
```

## 🔄 迁移指南

### 从旧架构迁移

1. **更新导入**:
   ```rust
   // 旧的
   use state_manager::{PoolState, PoolManager};
   
   // 新的
   use state_manager::core::types::*;
   use state_manager::core::pool::*;
   use state_manager::core::manager::*;
   ```

2. **使用统一管理器**:
   ```rust
   // 旧的 - 直接使用特定管理器
   let raydium_manager = RaydiumClmmPoolManager::new(state);
   
   // 新的 - 通过统一管理器
   let mut unified_manager = UnifiedPoolManager::with_default_config();
   unified_manager.register_pool_manager(pool_id, Arc::new(raydium_manager));
   ```

3. **更新错误处理**:
   ```rust
   // 旧的
   use shared::{EchoesError, Result};
   
   // 新的
   use state_manager::core::types::{PoolError, PoolResult};
   ```

## 🧪 测试

运行所有测试：
```bash
cd crates/state-manager
cargo test
```

运行特定模块测试：
```bash
cargo test core::
cargo test dex::raydium::
cargo test dex::meteora::
cargo test utils::
```

运行示例：
```bash
cargo run --example unified_manager_example
cargo run --example meteora_dlmm_example
```

## 🔮 未来扩展

### 计划添加的 DEX

- **Orca**: Whirlpool 集中流动性池
- **Jupiter**: 聚合器和路由优化
- **Serum**: 订单簿 DEX
- **Phoenix**: 新一代订单簿

### 计划添加的池类型

- **Stable Pools**: 稳定币优化的池
- **Weighted Pools**: 多代币加权池
- **LBP**: 流动性引导池
- **Infinity Pools**: 无限流动性池

### 高级功能

- **路由优化**: 多跳交换路径优化
- **MEV 保护**: 抢跑和三明治攻击保护
- **实时数据流**: WebSocket 实时价格更新
- **历史数据**: 价格和交易量历史分析
- **套利检测**: 跨 DEX 套利机会识别

## 📝 贡献指南

1. **添加新的 DEX**:
   - 在 `src/dex/` 下创建新目录
   - 实现 `PoolState` 和 `PoolManager` trait
   - 添加相应的类型定义和测试

2. **添加新的池类型**:
   - 在相应的 DEX 目录下添加新模块
   - 实现特定的计算逻辑
   - 更新统一管理器以支持新类型

3. **改进工具函数**:
   - 在 `src/utils/` 下添加新的工具模块
   - 确保函数具有良好的测试覆盖率
   - 添加文档和使用示例

这个新架构为 State Manager 提供了强大的扩展性和维护性，能够轻松支持新的 DEX 协议和池类型，同时保持代码的清晰和高效。
