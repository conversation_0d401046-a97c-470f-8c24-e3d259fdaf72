[package]
name = "state-manager"
version.workspace = true
edition.workspace = true

[dependencies]
shared = { path = "../shared" }
data-parser = { path = "../data-parser" }
solana-sdk.workspace = true
thiserror.workspace = true
tokio = { workspace = true, features = ["fs"] }
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
tracing.workspace = true

[dev-dependencies]
tempfile = "3.8"