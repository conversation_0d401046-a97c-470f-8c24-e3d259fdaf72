use std::str::FromStr;
use async_trait::async_trait;
use solana_sdk::bs58;
use shared::Result;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::instruction::CompiledInstruction;
use solana_sdk::transaction::VersionedTransaction;
use solana_transaction_status::{EncodedTransactionWithStatusMeta, UiCompiledInstruction, UiInnerInstructions, UiInstruction};
use yellowstone_grpc_proto::prost_types::Timestamp;
pub(crate) use crate::events::{DexEvent, DexEventType, ProtocolType};
use crate::parse_transfer_datas_from_instructions;

/// DEX解析器trait - 借鉴sol-feeder的EventParser设计
#[async_trait]
pub trait DexParser: Send + Sync {
    /// 获取解析器名称
    fn name(&self) -> &str;

    /// 获取支持的程序ID列表
    fn supported_program_ids(&self) -> Vec<Pubkey>;

    /// 检查是否应该处理此程序ID
    fn should_handle(&self, program_id: &Pubkey) -> bool;

    /// 从指令中解析事件数据
    fn parse_events_from_instruction(
        &self,
        instruction: &CompiledInstruction,
        accounts: &[Pubkey],
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Vec<Box<dyn DexEvent>>;

    /// 从内联指令中解析事件数据
    fn parse_events_from_inner_instruction(
        &self,
        instruction: &UiCompiledInstruction,
        signature: &str,
        slot: u64,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Vec<Box<dyn DexEvent>>;

    /// 解析交易数据，返回DEX事件列表 - 参考sol-feeder的parse_transaction方法
    async fn parse_transaction(
        &self,
        tx: EncodedTransactionWithStatusMeta,
        signature: &str,
        slot: Option<u64>,
        block_time: Option<Timestamp>,
        bot_wallet: Option<Pubkey>,
    ) -> Result<Vec<Box<dyn DexEvent>>> {
        let transaction = tx.transaction;
        // 检查交易元数据
        let meta = tx
            .meta
            .as_ref().unwrap();

        let mut address_table_lookups: Vec<Pubkey> = vec![];
        let mut inner_instructions: Vec<UiInnerInstructions> = vec![];
        if meta.err.is_none() {
            inner_instructions = meta.inner_instructions.as_ref().unwrap().clone();
            let loaded_addresses = meta.loaded_addresses.as_ref().unwrap();
            for lookup in &loaded_addresses.writable {
                address_table_lookups.push(Pubkey::from_str(lookup).unwrap());
            }
            for lookup in &loaded_addresses.readonly {
                address_table_lookups.push(Pubkey::from_str(lookup).unwrap());
            }
        }
        let mut accounts: Vec<Pubkey> = vec![];
        let mut instruction_events = Vec::new();

        // 解析指令事件
        if let Some(versioned_tx) = transaction.decode() {
            accounts = versioned_tx.message.static_account_keys().to_vec();
            accounts.extend(address_table_lookups.clone());

            instruction_events = self
                .parse_instruction_events_from_versioned_transaction(
                    &versioned_tx,
                    signature,
                    slot,
                    block_time,
                    &accounts,
                    &inner_instructions,
                )
                .await
                .unwrap_or_else(|_e| vec![]);
        } else {
            accounts.extend(address_table_lookups.clone());
        }


        // 解析内联指令事件
        let mut inner_instruction_events = Vec::new();
        // 检查交易是否成功
        if meta.err.is_none() {
            for inner_instruction in inner_instructions {
                for (index, instruction) in inner_instruction.instructions.iter().enumerate() {
                    match instruction {
                        UiInstruction::Compiled(compiled) => {
                            // 解析嵌套指令
                            let compiled_instruction = CompiledInstruction {
                                program_id_index: compiled.program_id_index,
                                accounts: compiled.accounts.clone(),
                                data: bs58::decode(compiled.data.clone()).into_vec().unwrap(),
                            };
                            if let Ok(mut events) = self
                                .parse_instruction(
                                    &compiled_instruction,
                                    &accounts,
                                    signature,
                                    slot,
                                    block_time,
                                    format!("{}.{}", inner_instruction.index, index),
                                )
                                .await
                            {
                                if events.len() > 0 {
                                    events.iter_mut().for_each(|event| {
                                        let transfer_datas =
                                            parse_transfer_datas_from_instructions(
                                                &inner_instruction,
                                                index as i8,
                                                &accounts,
                                                event.event_type(),
                                            );
                                        event.set_transfer_datas(transfer_datas.clone());
                                    });
                                    instruction_events.extend(events);
                                }
                            }
                            if let Ok(mut events) = self
                                .parse_inner_instruction(
                                    compiled,
                                    signature,
                                    slot,
                                    block_time,
                                    format!("{}.{}", inner_instruction.index, index),
                                )
                                .await
                            {
                                if events.len() > 0 {
                                    events.iter_mut().for_each(|event| {
                                        let transfer_datas =
                                            parse_transfer_datas_from_instructions(
                                                &inner_instruction,
                                                index as i8,
                                                &accounts,
                                                event.event_type(),
                                            );
                                        event.set_transfer_datas(transfer_datas.clone());
                                    });
                                    inner_instruction_events.extend(events);
                                }
                            }
                        }
                        _ => {}
                    }
                }
            }
        }

        if instruction_events.len() > 0 && inner_instruction_events.len() > 0 {
            for instruction_event in &mut instruction_events {
                for inner_instruction_event in &inner_instruction_events {
                    if instruction_event.id() == inner_instruction_event.id() {
                        let i_index = instruction_event.index();
                        let in_index = inner_instruction_event.index();
                        if !i_index.contains(".") && in_index.contains(".") {
                            let in_index_parent_index = in_index.split(".").nth(0).unwrap();
                            if in_index_parent_index == i_index {
                                instruction_event.merge(inner_instruction_event.clone_boxed());
                                break;
                            }
                        } else if i_index.contains(".") && in_index.contains(".") {
                            // 嵌套指令
                            let i_index_parent_index = i_index.split(".").nth(0).unwrap();
                            let in_index_parent_index = in_index.split(".").nth(0).unwrap();
                            if i_index_parent_index == in_index_parent_index {
                                let i_index_child_index = i_index.split(".").nth(1).unwrap();
                                let in_index_child_index = in_index.split(".").nth(1).unwrap();
                                if in_index_child_index > i_index_child_index {
                                    instruction_event.merge(inner_instruction_event.clone_boxed());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        Ok(self.process_events(instruction_events, bot_wallet))
    }

    /// 从VersionedTransaction中解析指令事件的通用方法
    async fn parse_instruction_events_from_versioned_transaction(
        &self,
        versioned_tx: &VersionedTransaction,
        signature: &str,
        slot: Option<u64>,
        block_time: Option<Timestamp>,
        accounts: &[Pubkey],
        inner_instructions: &[UiInnerInstructions],
    ) -> Result<Vec<Box<dyn DexEvent>>> {
        let mut instruction_events = Vec::new();
        // 获取交易的指令和账户
        let compiled_instructions = versioned_tx.message.instructions();
        let mut accounts: Vec<Pubkey> = accounts.to_vec();

        // 检查交易中是否包含程序
        let has_program = accounts.iter().any(|account| self.should_handle(account));
        if has_program {
            // 解析每个指令
            for (index, instruction) in compiled_instructions.iter().enumerate() {
                if let Some(program_id) = accounts.get(instruction.program_id_index as usize) {
                    if self.should_handle(program_id) {
                        let max_idx = instruction.accounts.iter().max().unwrap_or(&0);
                        // 补齐accounts(使用Pubkey::default())
                        if *max_idx as usize > accounts.len() {
                            for _i in accounts.len()..*max_idx as usize {
                                accounts.push(Pubkey::default());
                            }
                        }
                        if let Ok(mut events) =
                            self.parse_instruction(
                                instruction,
                                &accounts,
                                signature,
                                slot,
                                block_time,
                                format!("{}", index),
                            ).await
                        {
                            if events.len() > 0 {
                                if let Some(inn) =inner_instructions
                                    .iter()
                                    .find(|inner_instruction| {
                                        inner_instruction.index == index as u8
                                    })
                                {
                                    events.iter_mut().for_each(|event| {
                                        let transfer_datas =
                                            parse_transfer_datas_from_instructions(
                                                &inn,
                                                -1,
                                                &accounts,
                                                event.event_type(),
                                            );
                                        event.set_transfer_datas(transfer_datas.clone());
                                    });
                                }
                                instruction_events.extend(events);
                            }
                        }
                    }
                }
            }
        }
        Ok(instruction_events)
    }

    /// 获取解析器版本
    fn version(&self) -> &str {
        "1.0.0"
    }

    /// 获取解析器优先级（数字越大优先级越高）
    fn priority(&self) -> u8 {
        100
    }

    /// 健康检查
    async fn health_check(&self) -> Result<()> {
        Ok(())
    }

    /// 获取协议类型
    fn protocol(&self) -> ProtocolType;

    /// 从单个指令解析事件 - 内部辅助方法
    async fn parse_instruction(
        &self,
        instruction: &CompiledInstruction,
        accounts: &[Pubkey],
        signature: &str,
        slot: Option<u64>,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Result<Vec<Box<dyn DexEvent>>> {
        let slot = slot.unwrap_or(0);
        let events = self.parse_events_from_instruction(
            instruction,
            accounts,
            signature,
            slot,
            block_time,
            index,
        );
        Ok(events)
    }

    /// 从内联指令解析事件 - 内部辅助方法
    async fn parse_inner_instruction(
        &self,
        instruction: &UiCompiledInstruction,
        signature: &str,
        slot: Option<u64>,
        block_time: Option<Timestamp>,
        index: String,
    ) -> Result<Vec<Box<dyn DexEvent>>> {
        let slot = slot.unwrap_or(0);
        let events = self.parse_events_from_inner_instruction(
            instruction,
            signature,
            slot,
            block_time,
            index,
        );
        Ok(events)
    }

    /// 处理事件列表，可以在这里添加后处理逻辑
    fn process_events(
        &self,
        events: Vec<Box<dyn DexEvent>>,
        _bot_wallet: Option<Pubkey>,
    ) -> Vec<Box<dyn DexEvent>> {
        // 默认实现：直接返回原始事件列表
        // 子类可以重写这个方法来添加特定的后处理逻辑
        events
    }
}

/// 解析结果
#[derive(Debug)]
pub struct ParseResult {
    /// 解析出的事件
    pub events: Vec<Box<dyn DexEvent>>,
    /// 解析器名称
    pub parser_name: String,
    /// 解析耗时（毫秒）
    pub parse_duration_ms: u64,
    /// 是否解析成功
    pub success: bool,
    /// 错误信息（如果有）
    pub error: Option<String>,
}

impl ParseResult {
    /// 创建成功的解析结果
    pub fn success(events: Vec<Box<dyn DexEvent>>, parser_name: String, duration_ms: u64) -> Self {
        Self {
            events,
            parser_name,
            parse_duration_ms: duration_ms,
            success: true,
            error: None,
        }
    }

    /// 创建失败的解析结果
    pub fn failure(parser_name: String, duration_ms: u64, error: String) -> Self {
        Self {
            events: Vec::new(),
            parser_name,
            parse_duration_ms: duration_ms,
            success: false,
            error: Some(error),
        }
    }

    /// 获取事件数量
    pub fn event_count(&self) -> usize {
        self.events.len()
    }

    /// 检查是否有事件
    pub fn has_events(&self) -> bool {
        !self.events.is_empty()
    }
}

/// 解析器错误类型
#[derive(Debug, thiserror::Error)]
pub enum ParserError {
    #[error("指令数据太短: 需要 {required} 字节，但只有 {actual} 字节")]
    InsufficientData { required: usize, actual: usize },

    #[error("不支持的指令类型: {discriminator:?}")]
    UnsupportedInstruction { discriminator: Vec<u8> },

    #[error("账户索引超出范围: {index}")]
    AccountIndexOutOfRange { index: usize },

    #[error("反序列化失败: {message}")]
    DeserializationError { message: String },

    #[error("程序ID不匹配: 期望 {expected}，实际 {actual}")]
    ProgramIdMismatch { expected: Pubkey, actual: Pubkey },

    #[error("通用解析错误: {message}")]
    Generic { message: String },
}

impl ParserError {
    pub fn insufficient_data(required: usize, actual: usize) -> Self {
        Self::InsufficientData { required, actual }
    }

    pub fn unsupported_instruction(discriminator: Vec<u8>) -> Self {
        Self::UnsupportedInstruction { discriminator }
    }

    pub fn account_index_out_of_range(index: usize) -> Self {
        Self::AccountIndexOutOfRange { index }
    }

    pub fn deserialization_error(message: impl Into<String>) -> Self {
        Self::DeserializationError { message: message.into() }
    }

    pub fn program_id_mismatch(expected: Pubkey, actual: Pubkey) -> Self {
        Self::ProgramIdMismatch { expected, actual }
    }

    pub fn generic(message: impl Into<String>) -> Self {
        Self::Generic { message: message.into() }
    }
}

/// 解析器辅助函数
pub mod parser_utils {
    use super::*;

    /// 安全地获取账户地址
    pub fn get_account_at_index(accounts: &[Pubkey], index: usize) -> std::result::Result<Pubkey, ParserError> {
        accounts.get(index).copied()
            .ok_or_else(|| ParserError::account_index_out_of_range(index))
    }

    /// 验证账户索引的有效性
    pub fn validate_account_indices(indices: &[u8], account_count: usize) -> bool {
        indices.iter().all(|&idx| (idx as usize) < account_count)
    }

    /// 检查指令数据长度
    pub fn check_data_length(data: &[u8], required: usize) -> std::result::Result<(), ParserError> {
        if data.len() < required {
            return Err(ParserError::insufficient_data(required, data.len()));
        }
        Ok(())
    }

    /// 提取指令鉴别器
    pub fn extract_discriminator(data: &[u8]) -> Option<&[u8]> {
        if data.len() >= 8 {
            Some(&data[0..8])
        } else {
            None
        }
    }

    /// 比较指令鉴别器
    pub fn matches_discriminator(data: &[u8], expected: &[u8]) -> bool {
        if let Some(discriminator) = extract_discriminator(data) {
            discriminator == expected
        } else {
            false
        }
    }

    pub fn matches_discriminator_str(data: &str, expected: &str) -> bool {
        if data.len() < expected.len() {
            return false;
        }
        &data[..expected.len()] == expected
    }

    /// 从区块时间戳转换为毫秒
    pub fn timestamp_to_ms(timestamp: Option<Timestamp>) -> i64 {
        match timestamp {
            Some(ts) => ts.seconds * 1000 + (ts.nanos / 1_000_000) as i64,
            None => chrono::Utc::now().timestamp_millis(),
        }
    }

    /// 生成事件ID
    pub fn generate_event_id(signature: &str, event_type: DexEventType, additional: &str) -> String {
        use std::hash::{DefaultHasher, Hash, Hasher};

        let combined = format!("{}-{}-{}", signature, event_type.to_string(), additional);
        let mut hasher = DefaultHasher::new();
        combined.hash(&mut hasher);
        let hash_value = hasher.finish();
        format!("{:x}", hash_value)
    }
}
