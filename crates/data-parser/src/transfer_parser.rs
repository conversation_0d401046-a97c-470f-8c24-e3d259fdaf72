use solana_sdk::pubkey::Pubkey;
use solana_sdk::bs58;
use solana_transaction_status::{UiInnerInstructions, UiInstruction, UiCompiledInstruction};
use crate::events::{TransferData, DexEventType};

/// 解析来自指令中的token转账数据 - 借鉴sol-feeder的实现
pub fn parse_transfer_datas_from_instructions(
    inner_instruction: &UiInnerInstructions,
    current_index: i8,
    accounts: &[Pubkey],
    event_type: DexEventType,
) -> Vec<TransferData> {
    let take = match event_type {
        DexEventType::PumpFunBuy => 4,
        DexEventType::PumpFunSell => 1,
        DexEventType::BonkBuyExactIn
        | DexEventType::BonkBuyExactOut
        | DexEventType::BonkSellExactIn
        | DexEventType::BonkSellExactOut => 3,
        DexEventType::RaydiumCpmmSwapBaseInput
        | DexEventType::RaydiumCpmmSwapBaseOutput
        | DexEventType::RaydiumClmmSwap
        | DexEventType::RaydiumClmmSwapV2 => 2,
        _ => 0,
    };
    
    if take == 0 {
        return vec![];
    }
    
    let mut transfer_datas = vec![];
    
    // 获取当前指令之后的指定数量指令
    let next_instructions: Vec<&UiInstruction> = inner_instruction
        .instructions
        .iter()
        .skip((current_index + 1) as usize)
        .take(take)
        .collect();

    for instruction in next_instructions {
        if let UiInstruction::Compiled(compiled) = instruction {
            if let Ok(data) = bs58::decode(compiled.data.clone()).into_vec() {
                if data.is_empty() {
                    continue;
                }
                
                // Token Program: transferChecked (指令ID: 12)
                // Token 2022 Program: transferChecked
                if data[0] == 12 {
                    let account_pubkeys: Vec<Pubkey> = compiled
                        .accounts
                        .iter()
                        .filter_map(|&a| accounts.get(a as usize).copied())
                        .collect();
                        
                    if account_pubkeys.len() < 4 || data.len() < 10 {
                        continue;
                    }
                    
                    let (source, mint, destination, authority) = (
                        account_pubkeys[0],
                        account_pubkeys[1],
                        account_pubkeys[2],
                        account_pubkeys[3],
                    );
                    
                    if let Ok(amount_bytes) = data[1..9].try_into() {
                        let amount = u64::from_le_bytes(amount_bytes);
                        let decimals = data[9];
                        let token_program = accounts.get(compiled.program_id_index as usize)
                            .copied().unwrap_or_default();
                            
                        transfer_datas.push(TransferData {
                            token_program,
                            source,
                            destination,
                            authority: Some(authority),
                            amount,
                            decimals: Some(decimals),
                            mint: Some(mint),
                        });
                    }
                }
                // Token Program: transfer (指令ID: 3)
                else if data[0] == 3 {
                    let account_pubkeys: Vec<Pubkey> = compiled
                        .accounts
                        .iter()
                        .filter_map(|&a| accounts.get(a as usize).copied())
                        .collect();
                        
                    if account_pubkeys.len() < 3 || data.len() < 9 {
                        continue;
                    }
                    
                    let (source, destination, authority) = (
                        account_pubkeys[0], 
                        account_pubkeys[1], 
                        account_pubkeys[2]
                    );
                    
                    if let Ok(amount_bytes) = data[1..9].try_into() {
                        let amount = u64::from_le_bytes(amount_bytes);
                        let token_program = accounts.get(compiled.program_id_index as usize)
                            .copied().unwrap_or_default();
                            
                        transfer_datas.push(TransferData {
                            token_program,
                            source,
                            destination,
                            authority: Some(authority),
                            amount,
                            decimals: None,
                            mint: None,
                        });
                    }
                }
                // System Program: transfer (指令ID: 2)
                else if data[0] == 2 {
                    let account_pubkeys: Vec<Pubkey> = compiled
                        .accounts
                        .iter()
                        .filter_map(|&a| accounts.get(a as usize).copied())
                        .collect();
                        
                    if account_pubkeys.len() < 2 || data.len() < 12 {
                        continue;
                    }
                    
                    let (source, destination) = (account_pubkeys[0], account_pubkeys[1]);
                    
                    if let Ok(amount_bytes) = data[4..12].try_into() {
                        let amount = u64::from_le_bytes(amount_bytes);
                        let token_program = accounts.get(compiled.program_id_index as usize)
                            .copied().unwrap_or_default();
                            
                        transfer_datas.push(TransferData {
                            token_program,
                            source,
                            destination,
                            authority: None,
                            amount,
                            decimals: None,
                            mint: None,
                        });
                    }
                }
            }
        }
    }
    
    transfer_datas
}

/// 为事件设置转账数据的辅助函数
pub fn set_transfer_data_for_events(
    events: &mut [Box<dyn crate::events::DexEvent>],
    inner_instructions: &[UiInnerInstructions],
    accounts: &[Pubkey],
) {
    for event in events.iter_mut() {
        let index = event.index();
        let event_type = event.event_type();
        
        // 解析索引以找到对应的内部指令
        if let Ok(instruction_index) = index.parse::<u8>() {
            if let Some(inner_instruction) = inner_instructions
                .iter()
                .find(|inner| inner.index == instruction_index)
            {
                let transfer_datas = parse_transfer_datas_from_instructions(
                    inner_instruction,
                    -1, // 从开始搜索
                    accounts,
                    event_type,
                );
                event.set_transfer_datas(transfer_datas);
            }
        } else if index.contains('.') {
            // 处理嵌套指令索引 (例如 "1.2")
            let parts: Vec<&str> = index.split('.').collect();
            if parts.len() == 2 {
                if let (Ok(parent_index), Ok(child_index)) = (parts[0].parse::<u8>(), parts[1].parse::<i8>()) {
                    if let Some(inner_instruction) = inner_instructions
                        .iter()
                        .find(|inner| inner.index == parent_index)
                    {
                        let transfer_datas = parse_transfer_datas_from_instructions(
                            inner_instruction,
                            child_index,
                            accounts,
                            event_type,
                        );
                        event.set_transfer_datas(transfer_datas);
                    }
                }
            }
        }
    }
}