use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use shared::Result;
use solana_sdk::pubkey::Pubkey;
use crate::parser::DexParser;

/// 解析器元数据
#[derive(Debug, <PERSON><PERSON>)]
pub struct ParserMetadata {
    /// 解析器名称
    pub name: String,
    /// 版本
    pub version: String,
    /// 支持的程序ID
    pub supported_programs: Vec<String>,
    /// 优先级
    pub priority: u8,
    /// 描述
    pub description: String,
    /// 作者
    pub author: String,
}

/// 解析器性能统计
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct ParserStats {
    /// 总解析次数
    pub total_parses: u64,
    /// 成功解析次数
    pub successful_parses: u64,
    /// 总解析时间（毫秒）
    pub total_parse_time_ms: u64,
    /// 平均解析时间（毫秒）
    pub average_parse_time_ms: f64,
    /// 解析的事件总数
    pub total_events_parsed: u64,
}

/// 解析器注册表
///
/// 全局管理所有DEX解析器的注册、查找和生命周期
pub struct ParserRegistry {
    /// 注册的解析器（解析器名称 -> 解析器实例）
    parsers: Arc<RwLock<HashMap<String, Arc<dyn DexParser>>>>,
    /// 程序ID到解析器的映射（程序ID -> 解析器名称列表）
    program_to_parsers: Arc<RwLock<HashMap<String, Vec<String>>>>,
    /// 解析器统计信息
    stats: Arc<RwLock<HashMap<String, ParserStats>>>,
}

impl ParserRegistry {
    /// 创建新的解析器注册表
    pub fn new() -> Self {
        Self {
            parsers: Arc::new(RwLock::new(HashMap::new())),
            program_to_parsers: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 注册解析器
    pub fn register_parser(&self, parser: Arc<dyn DexParser>) -> Result<()> {
        let parser_name = parser.name().to_string();
        let program_ids = parser.supported_program_ids();

        // 检查解析器是否已注册
        {
            let parsers = self.parsers.read().unwrap();
            if parsers.contains_key(&parser_name) {
                return Err(shared::EchoesError::Config(
                    format!("Parser '{}' is already registered", parser_name)
                ));
            }
        }

        // 注册解析器
        {
            let mut parsers = self.parsers.write().unwrap();
            parsers.insert(parser_name.clone(), parser);
        }

        // 更新程序ID映射
        {
            let mut program_map = self.program_to_parsers.write().unwrap();
            for program_id in program_ids {
                program_map
                    .entry(program_id.to_string())
                    .or_insert_with(Vec::new)
                    .push(parser_name.clone());
            }
        }

        // 初始化统计信息
        {
            let mut stats = self.stats.write().unwrap();
            stats.insert(parser_name.clone(), ParserStats::default());
        }

        tracing::info!("Registered DEX parser: {}", parser_name);
        Ok(())
    }

    /// 注销解析器
    pub fn unregister_parser(&self, parser_name: &str) -> Result<()> {
        // 获取要移除的解析器
        let parser = {
            let mut parsers = self.parsers.write().unwrap();
            parsers.remove(parser_name)
        };

        if let Some(parser) = parser {
            let program_ids = parser.supported_program_ids();

            // 从程序ID映射中移除
            {
                let mut program_map = self.program_to_parsers.write().unwrap();
                for program_id in program_ids {
                    let program_id_str = program_id.to_string();
                    if let Some(parser_list) = program_map.get_mut(&program_id_str) {
                        parser_list.retain(|name| name != parser_name);
                        if parser_list.is_empty() {
                            program_map.remove(&program_id_str);
                        }
                    }
                }
            }

            // 移除统计信息
            {
                let mut stats = self.stats.write().unwrap();
                stats.remove(parser_name);
            }

            tracing::info!("Unregistered DEX parser: {}", parser_name);
            Ok(())
        } else {
            Err(shared::EchoesError::Config(
                format!("Parser '{}' not found", parser_name)
            ))
        }
    }

    /// 根据程序ID查找解析器
    pub fn find_parsers_for_program(&self, program_id: &Pubkey) -> Vec<Arc<dyn DexParser>> {
        let program_id_str = program_id.to_string();
        let program_map = self.program_to_parsers.read().unwrap();
        let parsers = self.parsers.read().unwrap();

        if let Some(parser_names) = program_map.get(&program_id_str) {
            parser_names
                .iter()
                .filter_map(|name| parsers.get(name).cloned())
                .collect()
        } else {
            Vec::new()
        }
    }

    /// 获取指定名称的解析器
    pub fn get_parser(&self, parser_name: &str) -> Option<Arc<dyn DexParser>> {
        let parsers = self.parsers.read().unwrap();
        parsers.get(parser_name).cloned()
    }

    /// 获取所有注册的解析器
    pub fn get_all_parsers(&self) -> Vec<Arc<dyn DexParser>> {
        let parsers = self.parsers.read().unwrap();
        parsers.values().cloned().collect()
    }

    /// 获取所有解析器的元数据
    pub fn get_parser_metadata(&self) -> Vec<ParserMetadata> {
        let parsers = self.parsers.read().unwrap();
        parsers
            .values()
            .map(|parser| {
                ParserMetadata {
                    name: parser.name().to_string(),
                    version: parser.version().to_string(),
                    supported_programs: parser.supported_program_ids().iter().map(|p| p.to_string()).collect(),
                    priority: parser.priority(),
                    description: format!("{} DEX解析器", parser.name()),
                    author: "Echoes Team".to_string(),
                }
            })
            .collect()
    }

    /// 获取解析器统计信息
    pub fn get_stats(&self) -> HashMap<String, ParserStats> {
        let stats = self.stats.read().unwrap();
        stats.clone()
    }

    /// 获取特定解析器的统计信息
    pub fn get_parser_stats(&self, parser_name: &str) -> Option<ParserStats> {
        let stats = self.stats.read().unwrap();
        stats.get(parser_name).cloned()
    }

    /// 更新解析器统计信息
    pub fn update_parser_stats<F>(&self, parser_name: &str, update_fn: F)
    where
        F: FnOnce(&mut ParserStats),
    {
        let mut stats = self.stats.write().unwrap();
        if let Some(parser_stats) = stats.get_mut(parser_name) {
            update_fn(parser_stats);
        }
    }

    /// 重置所有统计信息
    pub fn reset_all_stats(&self) {
        let mut stats = self.stats.write().unwrap();
        for stat in stats.values_mut() {
            *stat = ParserStats::default();
        }
    }

    /// 获取支持的程序ID列表
    pub fn get_supported_program_ids(&self) -> Vec<Pubkey> {
        let program_map = self.program_to_parsers.read().unwrap();
        program_map.keys()
            .filter_map(|s| s.parse().ok())
            .collect()
    }

    /// 检查程序ID是否被支持
    pub fn is_program_supported(&self, program_id: &Pubkey) -> bool {
        let program_id_str = program_id.to_string();
        let program_map = self.program_to_parsers.read().unwrap();
        program_map.contains_key(&program_id_str)
    }

    /// 获取注册的解析器数量
    pub fn parser_count(&self) -> usize {
        let parsers = self.parsers.read().unwrap();
        parsers.len()
    }

    /// 健康检查所有解析器
    pub async fn health_check_all(&self) -> HashMap<String, bool> {
        let parsers = self.get_all_parsers();
        let mut results = HashMap::new();

        for parser in parsers {
            let name = parser.name().to_string();
            let is_healthy = parser.health_check().await.is_ok();
            results.insert(name, is_healthy);
        }

        results
    }
}

impl Default for ParserRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局解析器注册表实例
static GLOBAL_REGISTRY: std::sync::OnceLock<ParserRegistry> = std::sync::OnceLock::new();

/// 获取全局解析器注册表
pub fn global_registry() -> &'static ParserRegistry {
    GLOBAL_REGISTRY.get_or_init(ParserRegistry::new)
}

/// 注册解析器到全局注册表
pub fn register_global_parser(parser: Arc<dyn DexParser>) -> Result<()> {
    global_registry().register_parser(parser)
}

/// 从全局注册表查找解析器
pub fn find_global_parsers_for_program(program_id: &Pubkey) -> Vec<Arc<dyn DexParser>> {
    global_registry().find_parsers_for_program(program_id)
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use shared::Result;
    use solana_transaction_status::{EncodedTransactionWithStatusMeta, UiCompiledInstruction};
    use yellowstone_grpc_proto::prost_types::Timestamp;
    use solana_sdk::instruction::CompiledInstruction;
    use crate::events::{DexEvent, ProtocolType};

    // 测试用解析器
    struct TestParser {
        name: String,
        program_ids: Vec<Pubkey>,
    }

    impl TestParser {
        fn new(name: &str, program_ids: Vec<&str>) -> Self {
            Self {
                name: name.to_string(),
                program_ids: program_ids.into_iter().map(|s| s.parse().unwrap()).collect(),
            }
        }
    }

    #[async_trait]
    impl DexParser for TestParser {
        fn name(&self) -> &str {
            &self.name
        }

        fn supported_program_ids(&self) -> Vec<Pubkey> {
            self.program_ids.clone()
        }

        fn should_handle(&self, program_id: &Pubkey) -> bool {
            self.program_ids.contains(program_id)
        }

        fn parse_events_from_instruction(
            &self,
            _instruction: &CompiledInstruction,
            _accounts: &[Pubkey],
            _signature: &str,
            _slot: u64,
            _block_time: Option<Timestamp>,
            _index: String,
        ) -> Vec<Box<dyn DexEvent>> {
            vec![]
        }

        fn parse_events_from_inner_instruction(
            &self,
            _instruction: &UiCompiledInstruction,
            _signature: &str,
            _slot: u64,
            _block_time: Option<Timestamp>,
            _index: String,
        ) -> Vec<Box<dyn DexEvent>> {
            vec![]
        }

        fn protocol(&self) -> ProtocolType {
            ProtocolType::RaydiumClmm
        }
    }

    #[test]
    fn test_parser_registry() {
        let registry = ParserRegistry::new();

        // 测试注册解析器
        let program_id1: Pubkey = "**************************111111".parse().unwrap();
        let program_id2: Pubkey = "22222222222222222222222222222222".parse().unwrap();

        let parser1 = Arc::new(TestParser::new("test1", vec!["**************************111111", "22222222222222222222222222222222"]));
        assert!(registry.register_parser(parser1.clone()).is_ok());

        // 测试重复注册
        let parser1_dup = Arc::new(TestParser::new("test1", vec!["33333333333333333333333333333333"]));
        assert!(registry.register_parser(parser1_dup).is_err());

        // 测试查找解析器
        let found_parsers = registry.find_parsers_for_program(&program_id1);
        assert_eq!(found_parsers.len(), 1);
        assert_eq!(found_parsers[0].name(), "test1");

        // 测试注销解析器
        assert!(registry.unregister_parser("test1").is_ok());
        assert!(registry.unregister_parser("test1").is_err());

        // 验证注销后查找不到
        let found_parsers = registry.find_parsers_for_program(&program_id1);
        assert_eq!(found_parsers.len(), 0);
    }
}
