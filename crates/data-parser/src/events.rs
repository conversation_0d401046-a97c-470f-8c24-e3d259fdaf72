use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::hash::{Default<PERSON>ash<PERSON>, Hash, <PERSON><PERSON>};
use solana_sdk::pubkey::Pubkey;
use yellowstone_grpc_proto::prost_types::Timestamp;

/// 协议类型枚举 - 借鉴sol-feeder的ProtocolType设计
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProtocolType {
    #[default]
    RaydiumClmm,
    RaydiumCpmm,
    PumpFun,
    Bonk
}

/// DEX事件类型枚举 - 参考sol-feeder的EventType设计
#[derive(Debug, <PERSON><PERSON>, Default, PartialEq, Eq, Serialize, Deserialize)]
pub enum DexEventType {
    #[default]
    RaydiumClmmSwap,
    RaydiumClmmSwapV2,
    RaydiumClmmIncreaseLiquidityV2,
    RaydiumClmmDecreaseLiquidityV2,
    RaydiumCpmmSwapBaseInput,
    RaydiumCpmmSwapBaseOutput,

    PumpSwapBuy,
    PumpSwapSell,
    PumpSwapCreatePool,
    PumpSwapDeposit,
    PumpSwapWithdraw,
    // PumpFun 事件
    PumpFunCreateToken,
    PumpFunBuy,
    PumpFunSell,

    BonkBuyExactIn,
    BonkBuyExactOut,
    BonkSellExactIn,
    BonkSellExactOut,
    BonkInitialize,
    Unknown,
}

impl DexEventType {
    pub fn to_string(&self) -> &str {
        match self {
            DexEventType::RaydiumClmmSwap => "RaydiumClmmSwap",
            DexEventType::RaydiumClmmSwapV2 => "RaydiumClmmSwapV2",
            DexEventType::RaydiumClmmIncreaseLiquidityV2 => "RaydiumClmmIncreaseLiquidityV2",
            DexEventType::RaydiumClmmDecreaseLiquidityV2 => "RaydiumClmmDecreaseLiquidityV2",
            DexEventType::RaydiumCpmmSwapBaseInput => "RaydiumCpmmSwapBaseInput",
            DexEventType::RaydiumCpmmSwapBaseOutput => "RaydiumCpmmSwapBaseOutput",
            DexEventType::PumpSwapBuy => "PumpSwapBuy",
            DexEventType::PumpSwapSell => "PumpSwapSell",
            DexEventType::PumpSwapCreatePool => "PumpSwapCreatePool",
            DexEventType::PumpSwapDeposit => "PumpSwapDeposit",
            DexEventType::PumpSwapWithdraw => "PumpSwapWithdraw",
            DexEventType::PumpFunCreateToken => "PumpFunCreateToken",
            DexEventType::PumpFunBuy => "PumpFunBuy",
            DexEventType::PumpFunSell => "PumpFunSell",
            DexEventType::BonkBuyExactIn => "BonkBuyExactIn",
            DexEventType::BonkBuyExactOut => "BonkBuyExactOut",
            DexEventType::BonkSellExactIn => "BonkSellExactIn",
            DexEventType::BonkSellExactOut => "BonkSellExactOut",
            DexEventType::BonkInitialize => "BonkInitialize",
            DexEventType::Unknown => "Unknown",
        }
    }
}

/// 事件元数据 - 借鉴sol-feeder的EventMetadata设计
#[derive(Debug, Clone, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct EventMetadata {
    pub id: String,
    pub signature: String,
    pub slot: u64,
    pub block_time: i64,
    pub block_time_ms: i64,
    pub program_received_time_ms: i64,
    pub protocol: ProtocolType,
    pub event_type: DexEventType,
    pub program_id: Pubkey,
    pub transfer_datas: Vec<TransferData>,
    pub index: String,
}

impl EventMetadata {
    pub fn new(
        id: String,
        signature: String,
        slot: u64,
        block_time: i64,
        block_time_ms: i64,
        protocol: ProtocolType,
        event_type: DexEventType,
        program_id: Pubkey,
        index: String,
    ) -> Self {
        Self {
            id,
            signature,
            slot,
            block_time,
            block_time_ms,
            program_received_time_ms: chrono::Utc::now().timestamp_millis(),
            protocol,
            event_type,
            program_id,
            transfer_datas: vec![],
            index,
        }
    }

    pub fn set_id(&mut self, id: String) {
        let _id = format!("{}-{}-{}", self.signature, self.event_type.to_string(), id);
        let mut hasher = DefaultHasher::new();
        _id.hash(&mut hasher);
        let hash_value = hasher.finish();
        self.id = format!("{:x}", hash_value);
    }
}

/// 转账数据 - 借鉴sol-feeder的TransferData
#[derive(Debug, Clone, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct TransferData {
    pub token_program: Pubkey,
    pub source: Pubkey,
    pub destination: Pubkey,
    pub authority: Option<Pubkey>,
    pub amount: u64,
    pub decimals: Option<u8>,
    pub mint: Option<Pubkey>,
}

/// 统一的DEX事件接口 - 借鉴sol-feeder的Event trait设计
pub trait DexEvent: std::fmt::Debug + Send + Sync {
    /// 获取事件ID
    fn id(&self) -> &str;

    /// 获取事件类型
    fn event_type(&self) -> DexEventType;

    /// 获取交易签名
    fn signature(&self) -> &str;

    /// 获取槽位号
    fn slot(&self) -> u64;

    /// 获取程序接收的时间戳(毫秒)
    fn program_received_time_ms(&self) -> i64;

    /// 获取协议类型
    fn protocol(&self) -> ProtocolType;

    /// 获取程序ID
    fn program_id(&self) -> &Pubkey;

    /// 获取指令索引
    fn index(&self) -> &str;

    /// 将事件转换为Any以便向下转型
    fn as_any(&self) -> &dyn std::any::Any;

    /// 将事件转换为可变Any以便向下转型
    fn as_any_mut(&mut self) -> &mut dyn std::any::Any;

    /// 克隆事件
    fn clone_boxed(&self) -> Box<dyn DexEvent>;

    /// 合并事件（可选实现）
    fn merge(&mut self, _other: Box<dyn DexEvent>) {
        // 默认实现：不进行任何合并操作
    }

    /// 设置转账数据
    fn set_transfer_datas(&mut self, transfer_datas: Vec<TransferData>);

    /// 获取转账数据
    fn get_transfer_datas(&self) -> &Vec<TransferData>;
}

/// 便捷的宏来为具体事件实现DexEvent trait
#[macro_export]
macro_rules! impl_dex_event {
    ($event_type:ty) => {
        impl DexEvent for $event_type {
            fn id(&self) -> &str {
                &self.metadata.id
            }

            fn event_type(&self) -> DexEventType {
                self.metadata.event_type.clone()
            }

            fn signature(&self) -> &str {
                &self.metadata.signature
            }

            fn slot(&self) -> u64 {
                self.metadata.slot
            }

            fn program_received_time_ms(&self) -> i64 {
                self.metadata.program_received_time_ms
            }

            fn protocol(&self) -> ProtocolType {
                self.metadata.protocol.clone()
            }

            fn program_id(&self) -> &Pubkey {
                &self.metadata.program_id
            }

            fn index(&self) -> &str {
                &self.metadata.index
            }

            fn as_any(&self) -> &dyn std::any::Any {
                self
            }

            fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
                self
            }

            fn clone_boxed(&self) -> Box<dyn DexEvent> {
                Box::new(self.clone())
            }

            fn set_transfer_datas(&mut self, transfer_datas: Vec<TransferData>) {
                self.metadata.transfer_datas = transfer_datas;
            }

            fn get_transfer_datas(&self) -> &Vec<TransferData> {
                &self.metadata.transfer_datas
            }
        }
    };
}
// ================ Raydium CLMM 事件 ================

/// Raydium CLMM 交换事件
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct RaydiumClmmSwapEvent {
    pub metadata: EventMetadata,
    pub amount: u64,
    pub other_amount_threshold: u64,
    pub sqrt_price_limit_x64: u128,
    pub is_base_input: bool,
    pub payer: Pubkey,
    pub amm_config: Pubkey,
    pub pool_state: Pubkey,
    pub input_token_account: Pubkey,
    pub output_token_account: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub observation_state: Pubkey,
    pub token_program: Pubkey,
    pub tick_array: Pubkey,
    pub remaining_accounts: Vec<Pubkey>,
}

impl_dex_event!(RaydiumClmmSwapEvent);

/// Raydium CLMM 交换事件 V2
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct RaydiumClmmSwapV2Event {
    pub metadata: EventMetadata,
    pub amount: u64,
    pub other_amount_threshold: u64,
    pub sqrt_price_limit_x64: u128,
    pub is_base_input: bool,
    pub payer: Pubkey,
    pub amm_config: Pubkey,
    pub pool_state: Pubkey,
    pub input_token_account: Pubkey,
    pub output_token_account: Pubkey,
    pub input_vault: Pubkey,
    pub output_vault: Pubkey,
    pub observation_state: Pubkey,
    pub token_program: Pubkey,
    pub token_program2022: Pubkey,
    pub memo_program: Pubkey,
    pub input_vault_mint: Pubkey,
    pub output_vault_mint: Pubkey,
    pub remaining_accounts: Vec<Pubkey>,
}

impl_dex_event!(RaydiumClmmSwapV2Event);

/// 流动性变化账户
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct LiquidityChangeAccounts {
    pub nft_owner: Pubkey,
    pub nft_account: Pubkey,
    pub pool_state: Pubkey,
    pub protocol_position: Pubkey,
    pub personal_position: Pubkey,
    pub tick_array_lower: Pubkey,
    pub tick_array_upper: Pubkey,
    pub token_account_0: Pubkey,
    pub token_account_1: Pubkey,
    pub token_vault_0: Pubkey,
    pub token_vault_1: Pubkey,
    pub token_program: Pubkey,
    pub token_program2022: Pubkey,
    pub vault_0_mint: Pubkey,
    pub vault_1_mint: Pubkey,
}

/// Raydium CLMM 增加流动性事件 V2
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct RaydiumClmmIncreaseLiquidityV2Event {
    pub metadata: EventMetadata,
    pub liquidity: u128,
    pub amount_0_max: u64,
    pub amount_1_max: u64,
    pub base_flag: Option<bool>,
    pub accounts: LiquidityChangeAccounts,
}

impl_dex_event!(RaydiumClmmIncreaseLiquidityV2Event);

/// Raydium CLMM 移除流动性事件 V2
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct RaydiumClmmDecreaseLiquidityV2Event {
    pub metadata: EventMetadata,
    pub liquidity: u128,
    pub amount_0_min: u64,
    pub amount_1_min: u64,
    pub accounts: LiquidityChangeAccounts,
}

impl_dex_event!(RaydiumClmmDecreaseLiquidityV2Event);

// ================ Raydium CPMM 事件 ================

/// Raydium CPMM 交换事件（基础输入）
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct RaydiumCpmmSwapBaseInputEvent {
    pub metadata: EventMetadata,
    pub amount_in: u64,
    pub minimum_amount_out: u64,
    pub pool_id: Pubkey,
    pub user_source_token_account: Pubkey,
    pub user_dest_token_account: Pubkey,
    pub pool_source_token_account: Pubkey,
    pub pool_dest_token_account: Pubkey,
    pub user: Pubkey,
}

impl_dex_event!(RaydiumCpmmSwapBaseInputEvent);

/// Raydium CPMM 交换事件（基础输出）
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct RaydiumCpmmSwapBaseOutputEvent {
    pub metadata: EventMetadata,
    pub max_amount_in: u64,
    pub amount_out: u64,
    pub pool_id: Pubkey,
    pub user_source_token_account: Pubkey,
    pub user_dest_token_account: Pubkey,
    pub pool_source_token_account: Pubkey,
    pub pool_dest_token_account: Pubkey,
    pub user: Pubkey,
}

impl_dex_event!(RaydiumCpmmSwapBaseOutputEvent);

// ================ PumpFun 事件 ================

/// PumpFun 代币创建事件
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct PumpFunCreateTokenEvent {
    pub metadata: EventMetadata,
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub user: Pubkey,
}

impl_dex_event!(PumpFunCreateTokenEvent);

/// PumpFun 交易事件
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct PumpFunTradeEvent {
    pub metadata: EventMetadata,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub user: Pubkey,
    pub is_buy: bool,
    pub amount: u64,
    pub sol_amount: u64,
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub real_token_reserves: u64,
}

impl_dex_event!(PumpFunTradeEvent);

// ================ Bonk 事件 ================

/// Bonk 交易事件
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct BonkTradeEvent {
    pub metadata: EventMetadata,
    pub pool_id: Pubkey,
    pub payer: Pubkey,
    pub amount_in: u64,
    pub amount_out: u64,
    pub input_mint: Pubkey,
    pub output_mint: Pubkey,
    pub is_exact_in: bool,
}

impl_dex_event!(BonkTradeEvent);

/// Bonk 池初始化事件
#[derive(Clone, Debug, Default, PartialEq, Eq, Serialize, Deserialize)]
pub struct BonkPoolCreateEvent {
    pub metadata: EventMetadata,
    pub pool_id: Pubkey,
    pub creator: Pubkey,
    pub token_a_mint: Pubkey,
    pub token_b_mint: Pubkey,
    pub fee_rate: u64,
}

impl_dex_event!(BonkPoolCreateEvent);
