[package]
name = "app"
version.workspace = true
edition.workspace = true

[dependencies]
shared = { path = "../shared" }
chain-listener = { path = "../chain-listener" }
data-parser = { path = "../data-parser" }

chrono = "0.4.41"
tokio = { workspace = true, features = ["rt", "rt-multi-thread", "macros", "sync"]}
tracing.workspace = true
tracing-appender.workspace = true
tracing-subscriber.workspace = true
