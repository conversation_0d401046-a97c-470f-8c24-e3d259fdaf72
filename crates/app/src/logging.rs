use std::path::PathBuf;
use chrono::Local;
use tracing::Level;
use tracing_appender::non_blocking::WorkerGuard;
use tracing_appender::{non_blocking, rolling};
use tracing_subscriber::fmt::format::Writer;
use tracing_subscriber::fmt::time::FormatTime;
use tracing_subscriber::fmt::writer::MakeWriterExt;

struct LocalTimer;


impl FormatTime for LocalTimer {
    fn format_time(&self, w: &mut Writer<'_>) -> std::fmt::Result {
        write!(w, "{}", Local::now().format("%Y-%m-%d %H:%M:%S"))
    }
}



pub fn init_logging() -> WorkerGuard {
    let logs_dir = PathBuf::from("./logs");

    let file_appender = rolling::daily(logs_dir, "app.log");
    let (non_blocking_writer, _guard) = non_blocking(file_appender);

    // a builder for `FmtSubscriber`.
    // 组合文件和控制台写入器
    let combined_writer = non_blocking_writer
        .with_max_level(Level::INFO)
        .and(std::io::stdout);

    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_file(true)
        .with_ansi(false)
        .with_line_number(true)
        .with_timer(LocalTimer)
        .with_writer(combined_writer)
        .init();

    _guard
}
