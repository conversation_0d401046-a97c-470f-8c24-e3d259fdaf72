use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::info;
use chain_listener::{ChainListenerConfig, DataHandler, SubscriptionConfig, YellowstoneGrpcClient};
use data_parser::{global_registry, register_global_parser, DexEvent, RaydiumClmmEventParser};
use shared::{DataType};
use crate::logging::init_logging;

mod logging;

#[tokio::main]
async fn main() {
    let _guard = init_logging();
    info!("🚀🚀🚀 Starting application");

    // 注册所有解析器
    setup_parsers().await;

    // 创建事件收集器
    let (event_tx, mut event_rx) = mpsc::unbounded_channel::<Box<dyn DexEvent>>();

    // 配置并启动链监听器
    let config = ChainListenerConfig::default();
    let client = YellowstoneGrpcClient::new(config);

    let subscription_config = SubscriptionConfig {
        programs: Some(vec![
            "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK".to_string(), // Raydium CLMM
        ]),
        vote: false,
        failed: false,
        ..Default::default()
    };

    let event_tx_clone = event_tx.clone();
    let data_handler: DataHandler = |data: DataType| -> Result<(), chain_listener::ChainListenerError> {
        if let DataType::Transaction(tx) = data {
            // 解析交易数据

            let slot = tx.slot;
            let signature = tx.signature.to_string();
            let bot_wallet = None;
            let parser = RaydiumClmmEventParser::new();


            let signature_clone = signature.clone();
            let bot_wallet_clone = bot_wallet.clone();

            tokio::spawn(async move {
                let parsers = global_registry().get_all_parsers();
                for parse in parsers {
                    let tx_clone = tx.tx.clone();
                    let res = parse.parse_transaction(
                        tx_clone,
                        &signature_clone,
                        Some(slot),
                        None,
                        bot_wallet_clone,
                    ).await.unwrap_or_else(|_e| vec![]);
                    println!("res: {:?}", res);
                }
            });
        }
        Ok(())
    };

    let res = client.subscribe_with_config(subscription_config, data_handler).await;
    if let Err(e) = res {
        println!("Error establishing subscription: {:?}", e);
        return;
    }
    println!("Subscription established successfully");

}


async fn setup_parsers() {
    let raydium_parser = Arc::new(RaydiumClmmEventParser::new());
    register_global_parser(raydium_parser).expect("Failed to register Raydium parser");
}
