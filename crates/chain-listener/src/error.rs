use thiserror::Error;
use yellowstone_grpc_client::{GeyserGrpcBuilderError, GeyserGrpcClientError};

/// 链监听器错误类型
#[derive(Error, Debug)]
pub enum ChainListenerError {
    #[error("Failed to build Yellowstone gRPC client: {0}")]
    GrpcBuilder(#[from] GeyserGrpcBuilderError),

    #[error("gRPC client error: {0}")]
    GrpcClient(#[from] GeyserGrpcClientError),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Connection error: {0}")]
    Connection(String),

    #[error("Subscription error: {0}")]
    Subscription(String),

    #[error("Message processing error: {0}")]
    MessageProcessing(String),
}

/// 链监听器结果类型
pub type ChainListenerResult<T> = Result<T, ChainListenerError>;