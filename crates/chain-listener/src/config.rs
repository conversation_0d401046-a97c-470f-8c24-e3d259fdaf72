/// 链监听器配置
#[derive(Debug, Clone)]
pub struct ChainListenerConfig {
    /// Yellowstone gRPC 端点
    pub endpoint: String,
    /// 认证Token
    pub x_token: Option<String>,
    /// 连接超时时间（秒）
    pub connect_timeout: u64,
    /// 请求超时时间（秒）
    pub request_timeout: u64,
    /// 是否保持连接
    pub keep_alive: bool,
}

impl Default for ChainListenerConfig {
    fn default() -> Self {
        Self {
            endpoint: "https://solana-yellowstone-grpc.publicnode.com:443".to_string(),
            x_token: None,
            connect_timeout: 10,
            request_timeout: 60,
            keep_alive: true,
        }
    }
}

impl ChainListenerConfig {
    /// 创建新的配置
    pub fn new(endpoint: String) -> Self {
        Self {
            endpoint,
            ..Default::default()
        }
    }

    /// 设置认证Token
    pub fn with_token(mut self, token: String) -> Self {
        self.x_token = Some(token);
        self
    }

    /// 设置连接超时
    pub fn with_connect_timeout(mut self, timeout: u64) -> Self {
        self.connect_timeout = timeout;
        self
    }

    /// 设置请求超时
    pub fn with_request_timeout(mut self, timeout: u64) -> Self {
        self.request_timeout = timeout;
        self
    }
}