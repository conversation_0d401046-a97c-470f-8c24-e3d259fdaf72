use std::sync::Arc;
use std::time::Duration;
use futures::channel::mpsc::SendError;
use futures_util::{Sink, SinkExt};
use tokio::sync::Mutex;
use tokio_stream::StreamExt;
use yellowstone_grpc_client::{ClientTlsConfig, GeyserGrpcClient, Interceptor};
use yellowstone_grpc_proto::geyser::{CommitmentLevel, SubscribeRequestPing};
use yellowstone_grpc_proto::geyser::subscribe_update::UpdateOneof;
use yellowstone_grpc_proto::prelude::{SubscribeRequest, SubscribeRequestFilterAccounts, SubscribeUpdate};
use shared::{AccountData, DataType, TransactionData};
use crate::{ChainListenerConfig, ChainListenerError};

/// Yellowstone gRPC 客户端
pub struct YellowstoneGrpcClient {
    config: ChainListenerConfig,
}

impl YellowstoneGrpcClient {
    /// 创建新的客户端实例
    pub fn new(config: ChainListenerConfig) -> Self {
        Self { config }
    }

    /// 构建底层gRPC客户端
    pub async fn build_client(
        &self,
    ) -> Result<Arc<Mutex<GeyserGrpcClient<impl Interceptor>>>, ChainListenerError> {
        let client = GeyserGrpcClient::build_from_shared(self.config.endpoint.clone())?
            .x_token(self.config.x_token.clone())?
            .tls_config(ClientTlsConfig::new().with_native_roots())?
            .connect_timeout(Duration::from_secs(self.config.connect_timeout))
            .keep_alive_while_idle(self.config.keep_alive)
            .timeout(Duration::from_secs(self.config.request_timeout))
            .connect()
            .await?;
        Ok(Arc::new(Mutex::new(client)))
    }

    /// 使用配置订阅数据流
    pub async fn subscribe_with_config(
        &self,
        config: SubscriptionConfig,
        data_handler: DataHandler,
    ) -> Result<(), ChainListenerError> {
        let client = self.build_client().await?;
        let subscribe_request = config.to_subscribe_request();

        tracing::debug!("SubscribeRequest: {:?}", subscribe_request);

        let (mut subscribe_tx, mut stream) = client
            .lock()
            .await
            .subscribe_with_request(Some(subscribe_request)).await?;

        while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => {
                    if let Err(e) = Self::message_handler(msg, &mut subscribe_tx, data_handler).await {
                        tracing::error!("Error handling message: {:?}", e);
                    }
                }
                Err(e) => {
                    tracing::error!("Error receiving message: {:?}", e);
                    break;
                }
            }
        }

        Ok(())
    }

    /// 处理订阅消息
    pub async fn message_handler(
        message: SubscribeUpdate,
        subscribe_tx: &mut (impl Sink<SubscribeRequest, Error = SendError> + std::marker::Unpin),
        handler: DataHandler,
    ) -> Result<(), ChainListenerError> {
        match message.update_oneof {
            Some(UpdateOneof::Transaction(sut)) => {
                let transaction_data: TransactionData = sut.into();
                if let Err(e) = handler(DataType::Transaction(transaction_data)) {
                    tracing::error!("Error handling transaction data: {:?}", e);
                }
            }
            Some(UpdateOneof::Account(account_update)) => {
                let account = AccountData {
                    pubkey: account_update.account.as_ref()
                        .map(|acc| bs58::encode(&acc.pubkey).into_string())
                        .unwrap_or_default(),
                    slot: account_update.slot,
                    lamports: account_update.account.as_ref()
                        .map(|acc| acc.lamports)
                        .unwrap_or_default(),
                    owner: account_update.account.as_ref()
                        .map(|acc| bs58::encode(&acc.owner).into_string())
                        .unwrap_or_default(),
                    data: account_update.account.as_ref()
                        .map(|acc| acc.data.clone())
                        .unwrap_or_default(),
                    executable: account_update.account.as_ref()
                        .map(|acc| acc.executable)
                        .unwrap_or_default(),
                    rent_epoch: account_update.account.as_ref()
                        .map(|acc| acc.rent_epoch)
                        .unwrap_or_default(),
                };
                if let Err(e) = handler(DataType::Account(account)) {
                    tracing::error!("Error handling account data: {:?}", e);
                }
            }
            Some(UpdateOneof::Ping(_)) => {
                let _ = subscribe_tx
                    .send(SubscribeRequest {
                        ping: Some(SubscribeRequestPing { id: 1 }),
                        ..Default::default()
                    })
                    .await;
                tracing::debug!("Ping sent successfully");
            }
            Some(UpdateOneof::Pong(_)) => {
                tracing::debug!("Pong received");
            }
            _ => {
                tracing::debug!("Received other update type");
            }
        }
        Ok(())
    }
}

/// 订阅配置
#[derive(Debug, Clone)]
pub struct SubscriptionConfig {
    /// 要监听的账户地址
    pub accounts: Option<Vec<String>>,
    /// 要监听的程序ID
    pub programs: Option<Vec<String>>,
    /// 排除的交易账户
    pub tx_account_exclude: Option<Vec<String>>,
    /// 必需的交易账户
    pub tx_account_required: Option<Vec<String>>,
    /// 是否订阅投票交易
    pub vote: bool,
    /// 是否订阅失败交易
    pub failed: bool,
}

impl Default for SubscriptionConfig {
    fn default() -> Self {
        Self {
            accounts: None,
            programs: None,
            tx_account_exclude: None,
            tx_account_required: None,
            vote: false,
            failed: false,
        }
    }
}

impl SubscriptionConfig {
    /// 转换为Yellowstone订阅请求
    pub fn to_subscribe_request(&self) -> SubscribeRequest {
        let mut request = SubscribeRequest::default();

        if let Some(accounts) = &self.accounts {
            request.accounts.insert(
                "client".to_string(),
                SubscribeRequestFilterAccounts {
                    account: accounts.clone(),
                    ..Default::default()
                },
            );
        }

        if let Some(programs) = &self.programs {
            request.transactions.insert(
                "client".to_string(),
                yellowstone_grpc_proto::prelude::SubscribeRequestFilterTransactions {
                    vote: Some(self.vote),
                    failed: Some(self.failed),
                    signature: None,
                    account_include: programs.clone(),
                    account_exclude: self.tx_account_exclude.clone().unwrap_or_default(),
                    account_required: self.tx_account_required.clone().unwrap_or_default(),
                },
            );
        }

        request.set_commitment(CommitmentLevel::Processed);
        request
    }
}

/// 数据处理函数类型
pub type DataHandler = fn(DataType) -> Result<(), ChainListenerError>;
