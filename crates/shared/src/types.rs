use solana_sdk::{pubkey::Pubkey, signature::Signature, transaction::VersionedTransaction};
use solana_transaction_status::{EncodedTransactionWithStatusMeta, TransactionStatusMeta, UiTransactionEncoding};
use yellowstone_grpc_proto::convert_from::create_tx_with_meta;
use yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction;
use serde::{Deserialize, Serialize};

/// 系统中流动的数据类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataType {
    Transaction(TransactionData),
    Account(AccountData),
    Block(BlockData),
    Slot(SlotData),
}

/// 账户数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountData {
    pub pubkey: String,
    pub slot: u64,
    pub lamports: u64,
    pub owner: String,
    pub data: Vec<u8>,
    pub executable: bool,
    pub rent_epoch: u64,
}

/// 区块数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockData {
    pub slot: u64,
    pub hash: String,
    pub parent_slot: u64,
    pub parent_hash: String,
    pub timestamp: Option<i64>,
}

/// 插槽数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlotData {
    pub slot: u64,
    pub parent: Option<u64>,
    pub status: String,
}

/// 交易数据结构（注意：某些字段无法序列化，需要特殊处理）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionData {
    pub slot: u64,
    #[serde(with = "signature_serde")]
    pub signature: Signature,
    pub index: u64,
    #[serde(skip)]
    pub meta: Option<TransactionStatusMeta>,
    #[serde(skip)]
    pub transaction: VersionedTransaction,
    #[serde(with = "pubkey_vec_serde")]
    pub account_keys: Vec<Pubkey>,
    pub tx: EncodedTransactionWithStatusMeta
}

// 自定义序列化模块for Signature
mod signature_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use solana_sdk::signature::Signature;
    use std::str::FromStr;

    pub fn serialize<S>(signature: &Signature, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        signature.to_string().serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Signature, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Signature::from_str(&s).map_err(serde::de::Error::custom)
    }
}

// 自定义序列化模块for Vec<Pubkey>
mod pubkey_vec_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    pub fn serialize<S>(pubkeys: &Vec<Pubkey>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let strings: Vec<String> = pubkeys.iter().map(|k| k.to_string()).collect();
        strings.serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Vec<Pubkey>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let strings = Vec::<String>::deserialize(deserializer)?;
        strings
            .into_iter()
            .map(|s| Pubkey::from_str(&s).map_err(serde::de::Error::custom))
            .collect()
    }
}

/// 可序列化的交易数据结构（用于消息传递）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerializableTransactionData {
    pub slot: u64,
    pub signature: String,
    pub index: u64,
    pub account_keys: Vec<String>,
    // 注意：这里省略了meta和transaction字段，因为它们包含复杂的内部类型
    // 实际项目中可能需要更完整的序列化策略
}

impl From<TransactionData> for SerializableTransactionData {
    fn from(tx: TransactionData) -> Self {
        Self {
            slot: tx.slot,
            signature: tx.signature.to_string(),
            index: tx.index,
            account_keys: tx.account_keys.iter().map(|k| k.to_string()).collect(),
        }
    }
}

impl From<SubscribeUpdateTransaction> for TransactionData {
    fn from(SubscribeUpdateTransaction { transaction, slot }: SubscribeUpdateTransaction) -> Self {
        let raw = transaction.expect("should be defined");
        let index = raw.index;
        let tx = create_tx_with_meta(raw).expect("valid tx with meta");
        Self {
            slot,
            index,
            signature: *tx.transaction_signature(),
            meta: tx.get_status_meta(),
            transaction: tx.get_transaction(),
            account_keys: tx.account_keys().iter().copied().collect(),
            tx: tx.encode(UiTransactionEncoding::Base64, Some(u8::MAX), true)
                .expect("failed to encode"),
        }
    }
}
