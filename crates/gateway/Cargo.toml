[package]
name = "gateway"
version.workspace = true
edition.workspace = true

[dependencies]
shared = { path = "../shared" }
chain-listener = { path = "../chain-listener" }
data-parser = { path = "../data-parser" }
state-manager = { path = "../state-manager" }
user-profiler = { path = "../user-profiler" }
arbitrage-engine = { path = "../arbitrage-engine" }
cex-connector = { path = "../cex-connector" }
persistence = { path = "../persistence" }
thiserror.workspace = true
tokio.workspace = true
async-trait.workspace = true
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true