use std::collections::HashMap;
use std::sync::Arc;
use std::any::{Any, TypeId};
use std::marker::PhantomData;
use async_trait::async_trait;
use tokio::sync::{broadcast, RwLock};
use serde::{Deserialize, Serialize, de::DeserializeOwned};
use shared::{EchoesError, Result};

/// 非泛型消息总线抽象trait（用于trait object）
#[async_trait]
pub trait MessageBus: Send + Sync {
    /// 发布序列化的消息到指定主题
    async fn publish_raw(&self, topic: &str, payload: Vec<u8>, type_name: &str) -> Result<()>;

    /// 订阅指定主题的消息（返回原始消息接收器）
    async fn subscribe_raw(&self, topic: &str) -> Result<broadcast::Receiver<MessageEnvelope>>;

    /// 健康检查
    async fn health_check(&self) -> Result<()>;

    /// 获取主题列表
    async fn list_topics(&self) -> Result<Vec<String>>;

    /// 获取主题的订阅者数量
    async fn subscriber_count(&self, topic: &str) -> Result<usize>;
}

/// 类型安全的消息总线包装器
pub struct TypedMessageBus {
    inner: Arc<dyn MessageBus>,
}

impl TypedMessageBus {
    pub fn new(bus: Arc<dyn MessageBus>) -> Self {
        Self { inner: bus }
    }

    /// 发布类型安全的消息
    pub async fn publish<T>(&self, topic: &str, message: T) -> Result<()>
    where
        T: Serialize + Send + Sync + 'static,
    {
        let payload = serde_json::to_vec(&message)
            .map_err(|e| EchoesError::Internal(format!("Serialization error: {}", e)))?;
        
        self.inner.publish_raw(topic, payload, std::any::type_name::<T>()).await
    }

    /// 订阅类型安全的消息
    pub async fn subscribe<T>(&self, topic: &str) -> Result<TypedReceiver<T>>
    where
        T: DeserializeOwned + Send + Sync + 'static,
    {
        let receiver = self.inner.subscribe_raw(topic).await?;
        Ok(TypedReceiver::new(receiver))
    }

    /// 代理健康检查
    pub async fn health_check(&self) -> Result<()> {
        self.inner.health_check().await
    }

    /// 代理主题列表
    pub async fn list_topics(&self) -> Result<Vec<String>> {
        self.inner.list_topics().await
    }

    /// 代理订阅者计数
    pub async fn subscriber_count(&self, topic: &str) -> Result<usize> {
        self.inner.subscriber_count(topic).await
    }
}

/// 类型安全的消息接收器
pub struct TypedReceiver<T> {
    receiver: broadcast::Receiver<MessageEnvelope>,
    _phantom: PhantomData<T>,
}

impl<T> TypedReceiver<T>
where
    T: DeserializeOwned,
{
    fn new(receiver: broadcast::Receiver<MessageEnvelope>) -> Self {
        Self {
            receiver,
            _phantom: PhantomData,
        }
    }

    /// 接收下一条消息
    pub async fn recv(&mut self) -> Result<T> {
        loop {
            let envelope = self.receiver.recv().await
                .map_err(|e| EchoesError::Internal(format!("Failed to receive message: {}", e)))?;

            // 尝试反序列化消息
            match serde_json::from_slice::<T>(&envelope.payload) {
                Ok(message) => return Ok(message),
                Err(_) => {
                    // 如果反序列化失败，继续等待下一条消息
                    // 这允许一个主题有多种消息类型
                    continue;
                }
            }
        }
    }

    /// 尝试接收消息（非阻塞）
    pub fn try_recv(&mut self) -> Result<Option<T>> {
        loop {
            match self.receiver.try_recv() {
                Ok(envelope) => {
                    match serde_json::from_slice::<T>(&envelope.payload) {
                        Ok(message) => return Ok(Some(message)),
                        Err(_) => continue, // 继续尝试下一条消息
                    }
                }
                Err(broadcast::error::TryRecvError::Empty) => return Ok(None),
                Err(e) => return Err(EchoesError::Internal(format!("Failed to receive message: {}", e))),
            }
        }
    }
}

/// 消息封装，用于类型擦除
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageEnvelope {
    pub topic: String,
    pub payload: Vec<u8>,
    pub message_type: String,
    pub timestamp: u64,
}

/// 基于Tokio channels的消息总线实现
pub struct ChannelMessageBus {
    /// 存储不同主题的广播发送器
    topics: Arc<RwLock<HashMap<String, broadcast::Sender<MessageEnvelope>>>>,
    /// 默认通道容量
    default_capacity: usize,
}

impl ChannelMessageBus {
    /// 创建新的通道消息总线
    pub fn new(default_capacity: usize) -> Self {
        Self {
            topics: Arc::new(RwLock::new(HashMap::new())),
            default_capacity,
        }
    }

    /// 获取或创建主题的发送器
    async fn get_or_create_sender(&self, topic: &str) -> broadcast::Sender<MessageEnvelope> {
        let mut topics = self.topics.write().await;
        
        if let Some(sender) = topics.get(topic) {
            sender.clone()
        } else {
            let (sender, _) = broadcast::channel(self.default_capacity);
            topics.insert(topic.to_string(), sender.clone());
            sender
        }
    }
}

#[async_trait]
impl MessageBus for ChannelMessageBus {
    async fn publish_raw(&self, topic: &str, payload: Vec<u8>, type_name: &str) -> Result<()> {
        let sender = self.get_or_create_sender(topic).await;
        
        let envelope = MessageEnvelope {
            topic: topic.to_string(),
            payload,
            message_type: type_name.to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };

        sender.send(envelope)
            .map_err(|e| EchoesError::Internal(format!("Failed to send message: {}", e)))?;

        Ok(())
    }

    async fn subscribe_raw(&self, topic: &str) -> Result<broadcast::Receiver<MessageEnvelope>> {
        let sender = self.get_or_create_sender(topic).await;
        Ok(sender.subscribe())
    }

    async fn health_check(&self) -> Result<()> {
        let topics = self.topics.read().await;
        if topics.len() <= 1000 {
            Ok(())
        } else {
            Err(EchoesError::Internal("Too many topics".to_string()))
        }
    }

    async fn list_topics(&self) -> Result<Vec<String>> {
        let topics = self.topics.read().await;
        Ok(topics.keys().cloned().collect())
    }

    async fn subscriber_count(&self, topic: &str) -> Result<usize> {
        let topics = self.topics.read().await;
        if let Some(sender) = topics.get(topic) {
            Ok(sender.receiver_count())
        } else {
            Ok(0)
        }
    }
}

/// 消息总线工厂
pub struct MessageBusFactory;

impl MessageBusFactory {
    /// 创建基于通道的消息总线
    pub fn create_channel_bus(capacity: usize) -> TypedMessageBus {
        let bus = Arc::new(ChannelMessageBus::new(capacity));
        TypedMessageBus::new(bus)
    }

    /// 创建Redis消息总线（占位符）
    pub fn create_redis_bus(_redis_url: &str) -> Result<TypedMessageBus> {
        // TODO: 实现Redis消息总线
        Err(EchoesError::Internal("Redis message bus not implemented yet".to_string()))
    }

    /// 创建Kafka消息总线（占位符）
    pub fn create_kafka_bus(_kafka_config: &str) -> Result<TypedMessageBus> {
        // TODO: 实现Kafka消息总线
        Err(EchoesError::Internal("Kafka message bus not implemented yet".to_string()))
    }
}

/// 预定义的主题常量
pub mod topics {
    pub const CHAIN_DATA: &str = "chain.data";
    pub const DEX_EVENTS: &str = "dex.events";
    pub const ARBITRAGE_OPPORTUNITIES: &str = "arbitrage.opportunities";
    pub const USER_ACTIVITIES: &str = "user.activities";
    pub const SYSTEM_HEALTH: &str = "system.health";
}

#[cfg(test)]
mod tests {
    use super::*;
    use shared::{DataType, AccountData};

    #[tokio::test]
    async fn test_channel_message_bus_basic() {
        let bus = MessageBusFactory::create_channel_bus(100);
        
        // 测试健康检查
        assert!(bus.health_check().await.is_ok());
        
        // 测试主题列表
        let topics = bus.list_topics().await.unwrap();
        assert!(topics.is_empty());
        
        // 测试订阅者计数
        let count = bus.subscriber_count("test_topic").await.unwrap();
        assert_eq!(count, 0);
    }

    #[tokio::test]
    async fn test_typed_message_passing() {
        let bus = MessageBusFactory::create_channel_bus(100);
        
        // 订阅AccountData消息
        let mut receiver = bus.subscribe::<AccountData>("test_topic").await.unwrap();
        
        // 发布消息
        let account = AccountData {
            pubkey: "test_pubkey".to_string(),
            slot: 12345,
            lamports: 1000000,
            owner: "test_owner".to_string(),
            data: vec![1, 2, 3, 4],
            executable: false,
            rent_epoch: 100,
        };
        
        bus.publish("test_topic", account.clone()).await.unwrap();
        
        // 接收消息
        let received = receiver.recv().await.unwrap();
        assert_eq!(received.pubkey, account.pubkey);
        assert_eq!(received.slot, account.slot);
    }
}