[workspace]
resolver = "2"
members = [
    "crates/shared",
    "crates/chain-listener",
    "crates/data-parser",
    "crates/state-manager",
    "crates/user-profiler",
    "crates/arbitrage-engine",
    "crates/cex-connector",
    "crates/persistence",
    "crates/gateway",
    "crates/app"
]



[workspace.package]
name = "echoes"
version = "0.1.0"
edition = "2024"

[workspace.dependencies]
async-trait = {version = "0.1"}
base64 = {version = "0.22.1"}
borsh = {version = "1.5.7"}
bs58 = {version = "0.5"}
chrono = {version = "0.4"}
futures = {version = "0.3.31"}
futures-util = {version = "0.3.31"}
hex = {version = "0.4.3"}
serde = {version = "1.0"}
serde_json = {version = "1.0"}
solana-sdk = {version = "2.3.1"}
solana-transaction-status = {version = "2.3.4"}
thiserror = {version = "2.0.12"}
tokio = {version = "1.46.1", features = ["rt", "rt-multi-thread", "macros", "sync"]}
tokio-stream = {version = "0.1.17"}
tracing = {version = "0.1"}
tracing-appender = {version = "0.2.3"}
tracing-subscriber = {version = "0.3"}
yellowstone-grpc-client = {version = "8.0.0"}
yellowstone-grpc-proto = {version = "8.0.0"}
