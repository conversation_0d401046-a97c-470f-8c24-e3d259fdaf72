# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Echoes is a Rust workspace project that provides Solana blockchain data streaming capabilities through the Yellowstone gRPC protocol. The project consists of two main crates in a workspace architecture:

- **shared**: Common utilities and shared code (currently minimal with placeholder functionality)
- **sol-feeder**: Main library for subscribing to Solana blockchain data via Yellowstone gRPC

## Architecture

The project uses a workspace-based architecture with shared dependencies managed at the workspace level. Key architectural components:

### YellowstoneGrpc Client (`crates/sol-feeder/src/client.rs`)
- Main client struct for connecting to Yellowstone gRPC endpoints
- Supports both account and transaction subscriptions
- Handles connection management, authentication (x-token), and TLS configuration
- Implements ping/pong keep-alive mechanism
- Uses async/await with tokio runtime

### Data Types (`crates/sol-feeder/src/format.rs`)
- Defines structured data types for different Solana primitives:
  - `TransactionData`: Contains slot, signature, metadata, and account keys
  - `AccountData`: Account state information
  - `BlockData`: Block-level information
  - `SlotData`: Slot progression data
- Implements conversion from Yellowstone gRPC protocol types

### Subscription Configuration
- `SubscriptionConfig` struct provides flexible subscription filtering
- Supports filtering by accounts, programs, vote transactions, and failed transactions
- Converts to Yellowstone gRPC `SubscribeRequest` format

## Common Development Commands

```bash
# Build the entire workspace
cargo build

# Build for production
cargo build --release

# Check code without building binaries
cargo check

# Run tests
cargo test

# Run the sol-feeder example
cargo run -p sol-feeder

# Format code
cargo fmt

# Lint code
cargo clippy

# Clean build artifacts
cargo clean
```

## Key Dependencies

- **yellowstone-grpc-client/proto**: Core gRPC client for Solana data streaming
- **solana-sdk**: Solana blockchain SDK for transaction and account types
- **tokio**: Async runtime with multi-threaded support
- **futures/futures-util**: Async stream handling
- **thiserror**: Error handling

## Development Notes

- Uses Rust 2024 edition
- All async code uses tokio runtime with multi-threading enabled
- Error handling follows Rust best practices with custom error types
- Main example connects to `https://solana-yellowstone-grpc.publicnode.com:443`
- Data handlers use function pointers for processing streamed data
- The codebase includes both high-level (`subscribe_with_config`) and low-level (`build_client`) APIs